from PIL import Image, ImageDraw
import os

# Create a new image with a dark background
width, height = 256, 256
image = Image.new('RGB', (width, height), (30, 30, 30))  # Very dark gray background
draw = ImageDraw.Draw(image)

# Define colors
black = (0, 0, 0)
white = (255, 255, 255)
dark_gray = (50, 50, 50)
medium_gray = (100, 100, 100)
light_gray = (180, 180, 180)

# Draw a document shape (invoice) with white outline on dark background
# Main document outline
draw.rectangle([(40, 40), (width-40, height-40)], fill=dark_gray, outline=white, width=5)

# Header
draw.rectangle([(40, 40), (width-40, 80)], fill=black, outline=white, width=1)

# Draw lines for text content - white lines on dark background
for i in range(3):
    y = 100 + (i * 40)
    draw.line([(60, y), (width-60, y)], fill=light_gray, width=4)

# Draw a simple table
table_y = 220
# Table header
draw.rectangle([(60, table_y), (width-60, table_y+30)], fill=medium_gray, outline=white, width=1)

# Save the image
image.save("icon.png")

# Save as ICO
try:
    image.save("icon.ico", sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)])
    print("Icon saved as icon.ico with multiple sizes")
except Exception as e:
    # Fallback method if the above fails
    icon_image = image.resize((32, 32))
    icon_image.save("icon.ico")
    print("Icon saved as icon.ico (32x32 only)")

print("Dark invoice icon created successfully!")
