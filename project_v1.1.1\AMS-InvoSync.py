from tkinter import filedialog
from PIL import Image, ImageTk
import tkinter as tk
import os
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image as RLImage
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from datetime import datetime, timedelta
from tkinter import ttk, messagebox, simpledialog
from num2words import num2words
from tkcalendar import DateEntry
import tempfile
import re
import json
import hashlib
import shutil

# Function to darken a color for button hover effects
def darken_color(hex_color, factor=0.8):
    """Darken a hex color by a factor (0-1)"""
    # Remove # if present
    hex_color = hex_color.lstrip('#')

    # Convert to RGB
    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)

    # Darken
    r = int(r * factor)
    g = int(g * factor)
    b = int(b * factor)

    # Convert back to hex
    return f"#{r:02x}{g:02x}{b:02x}"
import traceback
import sys
from activation_window import show_activation_window

# Global variables for authentication
current_user = None
user_type = None
limited_user = False  # Flag to easily check if user has limited access
is_authenticated = False

# Backup directory
BACKUP_DIR = r"C:\pukcab_XtfoSSMA\xtossmafiles\requried files\pukcab"

# Function to create backup of Excel files
def backup_excel_file(file_path):
    """
    Creates a backup of the specified Excel file in the backup directory.

    Args:
        file_path (str): Path to the Excel file to backup
    """
    try:
        # Create backup directory if it doesn't exist
        os.makedirs(BACKUP_DIR, exist_ok=True)

        # Get the filename from the path
        filename = os.path.basename(file_path)

        # Create backup path
        backup_path = os.path.join(BACKUP_DIR, filename)

        # Copy the file to the backup location
        shutil.copy2(file_path, backup_path)

        print(f"Backup created: {backup_path}")
    except Exception as e:
        print(f"Error creating backup: {str(e)}")

# Global variables
invoice_number = None
invoice_date = None
invoice_items = []
amount_words_value = None
subtotal_label = None
discount_label = None
sgst_label = None
cgst_label = None
grand_total_label = None
received_label = None
balance_label = None
preview_data = {}
bank_details = []
received_entry = None
payment_date = None

# Add these variables at the top with other global variables
product_edit_index = None
customer_edit_index = None

# Function to hash passwords
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

# Function to create users sheet in company profile Excel
def create_users_sheet():
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    os.makedirs(profile_dir, exist_ok=True)
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    # Check if file exists
    if os.path.exists(profile_file):
        try:
            # Load existing workbook
            wb = load_workbook(profile_file)

            # Check if Users sheet already exists
            if "Users" not in wb.sheetnames:
                # Create Users sheet
                users_sheet = wb.create_sheet("Users")

                # Add headers
                users_sheet["A1"] = "Username"
                users_sheet["B1"] = "Password"
                users_sheet["C1"] = "User Type"
                users_sheet["D1"] = "Secret Number"

                # DO NOT add default users - let the user create them during signup
                # Hide the Users sheet
                users_sheet.sheet_state = 'hidden'

                # Save the workbook
                wb.save(profile_file)
                print("Empty users sheet created successfully")
            else:
                print("Users sheet already exists")
        except Exception as e:
            print(f"Error creating users sheet: {e}")
    else:
        print("Company profile file does not exist yet")

# Function to verify login credentials
def verify_login(username, password):
    # Use company_profile.xlsx with hidden Users sheet
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    print(f"Verifying login for user: {username}")
    print(f"Profile file exists: {os.path.exists(profile_file)}")

    if not os.path.exists(profile_file):
        print("Profile file doesn't exist")
        return False, None

    try:
        # Load workbook
        wb = load_workbook(profile_file)
        print(f"Loaded profile workbook with sheets: {wb.sheetnames}")

        # Check if Users sheet exists
        if "Users" not in wb.sheetnames:
            print("Users sheet doesn't exist in profile file")
            return False, None

        # Get Users sheet
        users_sheet = wb["Users"]
        print(f"Users sheet has {users_sheet.max_row} rows")

        # Hash the provided password
        hashed_password = hash_password(password)

        # Check credentials
        for row in range(2, users_sheet.max_row + 1):
            sheet_username = users_sheet.cell(row=row, column=1).value
            print(f"Checking row {row}: {sheet_username}")

            if sheet_username == username and users_sheet.cell(row=row, column=2).value == hashed_password:
                user_type = users_sheet.cell(row=row, column=3).value
                print(f"Login successful for {username} with type {user_type}")
                return True, user_type

        print(f"Login failed for {username}")
        return False, None
    except Exception as e:
        print(f"Error verifying login: {e}")
        return False, None

# Function to get secret number for a username
def get_secret_number(username):
    # Use company_profile.xlsx with hidden Users sheet
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    print(f"Getting secret number for user: {username}")
    print(f"Profile file exists: {os.path.exists(profile_file)}")

    if not os.path.exists(profile_file):
        print("Profile file doesn't exist")
        return None

    try:
        # Load workbook
        wb = load_workbook(profile_file)
        print(f"Loaded profile workbook with sheets: {wb.sheetnames}")

        # Check if Users sheet exists
        if "Users" not in wb.sheetnames:
            print("Users sheet doesn't exist in profile file")
            return None

        # Get Users sheet
        users_sheet = wb["Users"]
        print(f"Users sheet has {users_sheet.max_row} rows")

        # Find username and get secret number
        for row in range(2, users_sheet.max_row + 1):
            sheet_username = users_sheet.cell(row=row, column=1).value
            print(f"Checking row {row}: {sheet_username}")

            if sheet_username == username:
                secret = users_sheet.cell(row=row, column=4).value
                print(f"Found secret number for {username}")
                return secret

        print(f"No secret number found for {username}")
        return None
    except Exception as e:
        print(f"Error getting secret number: {e}")
        return None

# Function to reset password
def reset_password(username, secret_number, new_password):
    # Use company_profile.xlsx with hidden Users sheet
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    print(f"Resetting password for user: {username}")
    print(f"Profile file exists: {os.path.exists(profile_file)}")

    if not os.path.exists(profile_file):
        print("Profile file doesn't exist")
        return False

    try:
        # Load workbook
        wb = load_workbook(profile_file)
        print(f"Loaded profile workbook with sheets: {wb.sheetnames}")

        # Check if Users sheet exists
        if "Users" not in wb.sheetnames:
            print("Users sheet doesn't exist in profile file")
            return False

        # Get Users sheet
        users_sheet = wb["Users"]
        print(f"Users sheet has {users_sheet.max_row} rows")

        # Find username and verify secret number
        for row in range(2, users_sheet.max_row + 1):
            sheet_username = users_sheet.cell(row=row, column=1).value
            sheet_secret = users_sheet.cell(row=row, column=4).value
            print(f"Checking row {row}: {sheet_username}, Secret: {sheet_secret}")

            if sheet_username == username and sheet_secret == secret_number:
                # Update password
                users_sheet.cell(row=row, column=2).value = hash_password(new_password)

                # Make sure the Users sheet remains hidden
                users_sheet.sheet_state = 'hidden'

                wb.save(profile_file)
                print(f"Password reset successful for {username}")
                return True

        print(f"Password reset failed for {username} - username or secret number incorrect")
        return False
    except Exception as e:
        print(f"Error resetting password: {e}")
        return False

# Function to create a new user - using company_profile.xlsx with hidden Users sheet
def create_user(username, password, user_type, secret_number):
    # Use company_profile.xlsx instead of separate files
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    os.makedirs(profile_dir, exist_ok=True)
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    try:
        # For debugging
        print(f"Creating user: {username} with type: {user_type}")
        print(f"Profile file path: {profile_file}")
        print(f"Profile file exists: {os.path.exists(profile_file)}")

        # Check if file exists
        if os.path.exists(profile_file):
            # Load existing workbook
            wb = load_workbook(profile_file)
            print(f"Loaded profile workbook with sheets: {wb.sheetnames}")
        else:
            # Create new workbook
            wb = Workbook()
            # Create Profile sheet if it doesn't exist
            if "Sheet" in wb.sheetnames:
                sheet = wb["Sheet"]
                sheet.title = "Profile"
            else:
                wb.create_sheet("Profile")
            print("Created new profile workbook")

        # Check if Users sheet exists
        if "Users" not in wb.sheetnames:
            # Create Users sheet
            users_sheet = wb.create_sheet("Users")
            print("Created Users sheet")
        else:
            users_sheet = wb["Users"]
            print(f"Found existing Users sheet with {users_sheet.max_row} rows")

        # Add headers if this is a new sheet
        if users_sheet.max_row <= 1:
            users_sheet["A1"] = "Username"
            users_sheet["B1"] = "Password"
            users_sheet["C1"] = "User Type"
            users_sheet["D1"] = "Secret Number"
            print("Added headers to Users sheet")

        # Check if username already exists
        for row in range(2, users_sheet.max_row + 1):
            if users_sheet.cell(row=row, column=1).value == username:
                print(f"Username {username} already exists")
                return False, "Username already exists"

        # Add new user
        next_row = users_sheet.max_row + 1
        users_sheet.cell(row=next_row, column=1).value = username
        users_sheet.cell(row=next_row, column=2).value = hash_password(password)
        users_sheet.cell(row=next_row, column=3).value = user_type
        users_sheet.cell(row=next_row, column=4).value = secret_number
        print(f"Added user {username} at row {next_row}")

        # Hide the Users sheet for privacy
        users_sheet.sheet_state = 'hidden'
        print("Users sheet hidden for privacy")

        # Save the workbook
        wb.save(profile_file)
        print(f"Saved profile workbook")

        # Create setup complete flag file
        flag_file = os.path.join(profile_dir, "setup_complete.flag")
        with open(flag_file, 'w') as f:
            f.write("Setup completed")
        print("Created setup complete flag file")

        return True, "User created successfully"
    except Exception as e:
        print(f"Error creating user: {e}")
        return False, f"Error: {str(e)}"

def update_bank_details():
    global bank_details
    bank_details = [
        f"Account Holder: {preview_data.get('Account Holder Name', '')}",
        f"Account Number: {preview_data.get('Account Number', '')}",
        f"IFSC Code: {preview_data.get('IFSC Code', '')}",
        f"Branch: {preview_data.get('Branch', '')}"
    ]

def refresh_invoice_number():
    """Refresh the invoice number and update it in the UI"""
    global invoice_number
    old_invoice_number = invoice_number
    invoice_number = generate_invoice_number()

    # Update invoice number in the UI
    # First, try to find the invoice number label in the invoice_number_frame
    updated = False

    for widget in frames["Invoice"].winfo_children():
        if isinstance(widget, tk.Canvas):
            for child in widget.winfo_children():
                for frame in child.winfo_children():
                    if isinstance(frame, tk.Frame):
                        for detail_frame in frame.winfo_children():
                            if isinstance(detail_frame, tk.Frame):
                                # Check if this is the invoice_number_frame
                                for label in detail_frame.winfo_children():
                                    if isinstance(label, tk.Label) and "Invoice No:" in label.cget("text"):
                                        label.config(text=f"Invoice No: {invoice_number}")
                                        updated = True

    # If we couldn't find the label, recreate the invoice content
    if not updated:
        # Clear any existing widgets in the invoice frame
        for widget in frames["Invoice"].winfo_children():
            widget.destroy()
        # Recreate the invoice content with the new invoice number
        create_invoice_content()

    return old_invoice_number, invoice_number

def generate_pdf():
    try:
        # Use the current invoice number for the PDF
        global invoice_number

        # Define the directory structure
        base_dir = os.path.join("D:", os.sep, "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice")

        # Create directory if it doesn't exist
        try:
            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
        except Exception as e:
            messagebox.showerror("Error", f"Could not create directory:\n{base_dir}\n\nError: {str(e)}")
            return

        # Create filename and full path
        filename = f"Invoice_INV{invoice_number}.pdf"
        filepath = os.path.join(base_dir, filename)

        # First try creating a temporary PDF to check if we can write
        temp_pdf = None
        try:
            temp_fd, temp_pdf = tempfile.mkstemp(suffix='.pdf')
            os.close(temp_fd)

            # Create the PDF document
            doc = SimpleDocTemplate(
                temp_pdf,
                pagesize=A4,
                rightMargin=40,
                leftMargin=40,
                topMargin=40,
                bottomMargin=30
            )

            elements = []

            # Define styles
            styles = getSampleStyleSheet()
            style_normal = ParagraphStyle(
                'Normal',
                parent=styles['Normal'],
                fontSize=10,
                leading=12,
                fontName='Helvetica'
            )
            style_title = ParagraphStyle(
                'Title',
                parent=styles['Title'],
                fontSize=16,
                leading=20,
                fontName='Helvetica',
                alignment=1,
                spaceAfter=20
            )

            # Add title
            elements.append(Paragraph("INVOICE", style_title))

            # Create header with logo and invoice details side by side
            header_data = [[]]

            # Left side: Invoice details and company address
            invoice_and_address = []

            # Invoice details
            invoice_details = [
                ["Invoice No:", str(invoice_number)],
                ["Date:", str(invoice_date)]
            ]
            invoice_table = Table(invoice_details, colWidths=[70, 130])
            invoice_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            # Company address with wrapping
            company_name = Paragraph(f"Company Name: {preview_data.get('Company Name', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))
            company_address = Paragraph(f"Address: {preview_data.get('Address', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))

            address_data = [[company_name], [company_address]]
            address_table = Table(address_data, colWidths=[200])
            address_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            # Company Details Section with wrapping
            company_email = Paragraph(f"Email: {preview_data.get('Email', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))
            company_gst = Paragraph(f"GST Number: {preview_data.get('GST Number', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))
            company_phone = Paragraph(f"Phone Number: {preview_data.get('Phone Number', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))
            company_state = Paragraph(f"State: {preview_data.get('State', '')}",
                ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12))

            company_data = [
                [company_email, company_gst],
                [company_phone, company_state]
            ]

            company_table = Table(company_data, colWidths=[doc.width/2, doc.width/2])
            company_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            # Combine invoice details and address
            invoice_and_address.append([invoice_table])
            invoice_and_address.append([address_table])
            combined_table = Table(invoice_and_address)
            combined_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ]))

            # Right side: Logo
            if "Company Logo" in preview_data and os.path.exists(preview_data["Company Logo"]):
                try:
                    logo = RLImage(preview_data["Company Logo"], width=100, height=80)
                    header_data[0] = [combined_table, logo]
                except Exception as e:
                    print(f"Error loading company logo: {e}")
                    header_data[0] = [combined_table, '']
            else:
                header_data[0] = [combined_table, '']

            # Create header table
            header_table = Table(header_data, colWidths=[doc.width * 0.7, doc.width * 0.3])
            header_table.setStyle(TableStyle([
                ('ALIGN', (-1, 0), (-1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ]))
            elements.append(header_table)
            elements.append(Spacer(1, 20))

            # Company Details Section (without address)
            company_data = [
                [Paragraph(f"Email: {preview_data.get('Email', '')}", style_normal),
                 Paragraph(f"GST Number: {preview_data.get('GST Number', '')}", style_normal)],
                [Paragraph(f"Phone Number: {preview_data.get('Phone Number', '')}", style_normal),
                 Paragraph(f"State: {preview_data.get('State', '')}", style_normal)]
            ]

            company_table = Table(company_data, colWidths=[doc.width/2, doc.width/2])
            company_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))
            elements.append(company_table)
            elements.append(Spacer(1, 20))

            # Customer Details Section
            customer_name = customer_search_var.get()
            customer = None
            if customer_name and customer_name != "Select Customer":
                customer = next((c for c in customers_data if c["Customer Name"] == customer_name), None)

            if customer:
                address = customer.get('Address', '').strip()
                shipping = customer.get('Shipping To', '').strip()
            else:
                address = customer_entries['Address'].get('1.0', 'end-1c').strip()
                shipping = customer_entries['Shipping To'].get('1.0', 'end-1c').strip()

            # Create customer details with proper formatting
            customer_details = []

            # Customer Name (full width)
            name_data = [[f"Customer Name: {customer.get('Customer Name', '') if customer else customer_entries['Customer Name'].get()}"]]
            name_table = Table(name_data, colWidths=[doc.width])
            name_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ]))
            customer_details.append([name_table])

            # Email and GST (half width each)
            contact_data = [
                [f"Email: {customer.get('Email', '') if customer else customer_entries['Email'].get()}",
                 f"GST Number: {customer.get('GST Number', '') if customer else customer_entries['GST Number'].get()}"]
            ]
            contact_table = Table(contact_data, colWidths=[doc.width/2, doc.width/2])
            contact_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ]))
            customer_details.append([contact_table])

            # Phone and State (half width each)
            phone_state_data = [
                [f"Phone Number: {customer.get('Phone Number', '') if customer else customer_entries['Phone Number'].get()}",
                 f"State: {customer.get('State', '') if customer else customer_entries['State'].get()}"]
            ]
            phone_state_table = Table(phone_state_data, colWidths=[doc.width/2, doc.width/2])
            phone_state_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ]))
            customer_details.append([phone_state_table])

            # Address (full width)
            address_data = [[f"Address: {address}"]]
            address_table = Table(address_data, colWidths=[doc.width])
            address_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ]))
            customer_details.append([address_table])

            # Shipping (full width)
            shipping_data = [[f"Shipping To: {shipping}"]]
            shipping_table = Table(shipping_data, colWidths=[doc.width])
            shipping_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ]))
            customer_details.append([shipping_table])

            # Combine all customer details
            customer_table = Table(customer_details)
            customer_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            elements.append(customer_table)
            elements.append(Spacer(1, 20))

            # Products Table
            products_data = [["Sr.", "Item Name", "Quantity", "Unit", "Price/Unit", "Discount", "GST", "Amount"]]

            for i, item in enumerate(invoice_items, 1):
                products_data.append([
                    str(i),
                    Paragraph(item['item_name'].get(), ParagraphStyle('normal', fontName='Helvetica', fontSize=10, leading=12)),
                    str(item['quantity'].get()),
                    str(item['unit'].get()),
                    str(item['price'].get()),
                    str(item['discount'].get()),
                    str(item['gst'].get()),
                    str(item['amount'].cget("text").replace("₹", ""))
                ])

            # Calculate column widths for products table - adjusted for better readability
            col_widths = [25, 200, 45, 45, 65, 45, 45, 65]  # Increased Item Name width

            products_table = Table(products_data, colWidths=col_widths, repeatRows=1)
            products_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Sr. No
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),    # Item Name
                ('ALIGN', (2, 0), (-1, -1), 'RIGHT'),  # All other columns
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Vertical alignment for all cells
            ]))
            elements.append(products_table)
            elements.append(Spacer(1, 10))

            # Calculation Details
            def clean_amount(text):
                # Remove label part and currency symbol
                text = text.split(":")[-1] if ":" in text else text
                text = text.replace("₹", "").replace(",", "").strip()
                try:
                    return "{:.2f}".format(float(text))
                except:
                    return "0.00"

            # Calculate amount in words
            try:
                total_text = grand_total_label.cget("text")
                total_text = total_text.replace("₹", "").replace(",", "").strip()
                if ":" in total_text:
                    total_text = total_text.split(":")[-1].strip()
                total_amount = float(total_text)
                amount_in_words = num2words(int(total_amount), lang='en_IN').title()

                # Fix decimal part calculation to ensure accuracy
                decimal_part = round((total_amount - int(total_amount)) * 100)
                if decimal_part > 0:
                    amount_in_words += f" Rupees and {num2words(decimal_part, lang='en_IN').title()} Paise Only"
                else:
                    amount_in_words += " Rupees Only"
            except Exception as e:
                print(f"Error converting amount to words: {e}")
                amount_in_words = "Zero Rupees Only"

            # Left side data (Received, Pending Amount, Amount in Words)
            left_data = [
                ["Received:", clean_amount(received_label.cget("text"))],
                ["Pending Amount:", clean_amount(balance_label.cget("text"))],
            ]

            # Create left side table for Received and Balance
            left_table = Table(left_data, colWidths=[80, doc.width/2 - 90])
            left_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),  # Right align labels
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # Right align amounts
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            # Create Amount in Words with proper wrapping
            amount_words_style = ParagraphStyle(
                'AmountWords',
                fontName='Helvetica',
                fontSize=10,
                leading=12,
                alignment=0,  # 0=Left alignment
                spaceAfter=6
            )
            amount_in_words_para = Paragraph(f"Amount in Words: {amount_in_words}", amount_words_style)
            amount_words_table = Table([[amount_in_words_para]], colWidths=[doc.width])
            amount_words_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ('LINEABOVE', (0, 0), (-1, 0), 0.5, colors.black),  # Line above Amount in Words
            ]))

            # Right side data (calculations)
            right_data = [
                ["Sub Total:", clean_amount(subtotal_label.cget("text"))],
                ["Discount:", clean_amount(discount_label.cget("text"))],
                ["SGST:", clean_amount(sgst_label.cget("text"))],
                ["CGST:", clean_amount(cgst_label.cget("text"))],
                ["Grand Total:", clean_amount(grand_total_label.cget("text"))]
            ]

            # Create right side table
            right_table = Table(right_data, colWidths=[80, 100])
            right_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),  # Right align labels
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # Right align amounts
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                ('LINEABOVE', (0, -1), (-1, -1), 0.5, colors.black),  # Line above Grand Total
            ]))

            # First combine left and right tables
            calc_data = [[left_table, right_table]]
            calc_table = Table(calc_data, colWidths=[doc.width/2, doc.width/2])
            calc_table.setStyle(TableStyle([
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('ALIGN', (0, 0), (0, 0), 'LEFT'),
                ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ]))

            elements.append(calc_table)
            elements.append(amount_words_table)
            elements.append(Spacer(1, 10))

            # Bank Details Section
            try:
                # Get bank details from preview_data
                Account_Holder_Name = preview_data.get('Account Holder Name', '')
                account_number = preview_data.get('Account Number', '')
                ifsc_code = preview_data.get('IFSC Code', '')
                branch = preview_data.get('Branch', '')

                if any([Account_Holder_Name, account_number, ifsc_code, branch]):  # Only add if there are bank details
                    # Create bank details header
                    bank_header = Table([["Bank Details:"]], colWidths=[doc.width])
                    bank_header.setStyle(TableStyle([
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 0),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                        ('TOPPADDING', (0, 0), (-1, -1), 3),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                    ]))
                    elements.append(bank_header)

                    # Create bank details in 2x2 grid
                    bank_data = [
                        [f"Account Holder Name: {Account_Holder_Name}", f"IFSC Code: {ifsc_code}"],
                        [f"Account Number: {account_number}", f"Branch: {branch}"]
                    ]

                    bank_table = Table(bank_data, colWidths=[doc.width/2 - 10, doc.width/2 - 10])
                    bank_table.setStyle(TableStyle([
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 10),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                        ('TOPPADDING', (0, 0), (-1, -1), 3),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
                    ]))
                    elements.append(bank_table)
                    elements.append(Spacer(1, 10))
            except Exception as e:
                print(f"Error adding bank details: {e}")

            # Terms and Conditions in 2x2 grid
            terms_data = []
            profile_terms = preview_data.get("Terms and Conditions", "").split('\n')

            # Create 2x2 grid from profile terms
            row1 = []
            row2 = []
            for i, term in enumerate(profile_terms):
                # Remove any existing numbers from the term
                term = term.strip()
                if term.startswith(('1.', '2.', '3.', '4.')):
                    term = term[2:].strip()
                elif term.startswith(('1)', '2)', '3)', '4)')):
                    term = term[2:].strip()

                if i < 2:
                    row1.append(f"{i+1}) {term}")
                elif i < 4:
                    row2.append(f"{i+1}) {term}")

            # Ensure we have 2 items in each row
            while len(row1) < 2:
                row1.append("")
            while len(row2) < 2:
                row2.append("")

            terms_data = [row1, row2]

            terms_table = Table(terms_data, colWidths=[doc.width/2 - 10, doc.width/2 - 10])
            terms_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            # Add Terms and Conditions header
            terms_header = Table([["Terms and Conditions:"]], colWidths=[doc.width])
            terms_header.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 3),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ]))

            elements.append(terms_header)
            elements.append(terms_table)
            elements.append(Spacer(1, 10))

            # Company Seal and Sign
            try:
                if "Stamp/Sign" in preview_data and os.path.exists(preview_data["Stamp/Sign"]):
                    # Create image object
                    seal_img = RLImage(preview_data["Stamp/Sign"])
                    seal_img.drawHeight = 80
                    seal_img.drawWidth = 100

                    # Create table for image
                    img_table = Table([[seal_img]], colWidths=[100])
                    img_table.setStyle(TableStyle([
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ]))

                    # Create text table
                    text_table = Table([["Company Seal and Sign"]], colWidths=[100])
                    text_table.setStyle(TableStyle([
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('TOPPADDING', (0, 0), (-1, -1), 5),
                    ]))

                    # Combine image and text in a vertical arrangement
                    seal_data = [[img_table], [text_table]]
                    seal_table = Table(seal_data)
                    seal_table.setStyle(TableStyle([
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('LEFTPADDING', (0, 0), (-1, -1), 0),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                    ]))

                    # Create wrapper table to position on right
                    wrapper_data = [[None, seal_table]]
                    wrapper_table = Table(wrapper_data, colWidths=[doc.width - 120, 120])
                    wrapper_table.setStyle(TableStyle([
                        ('LEFTPADDING', (0, 0), (-1, -1), 0),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                        ('ALIGN', (-1, 0), (-1, -1), 'RIGHT'),
                    ]))

                    elements.append(wrapper_table)
            except Exception as e:
                print(f"Error loading company seal/sign: {e}")

            # Build the PDF
            doc.build(elements)

            # If temporary PDF was created successfully, copy it to final location
            if os.path.exists(temp_pdf):
                import shutil
                shutil.copy2(temp_pdf, filepath)

                # Verify final file was created
                if os.path.exists(filepath):
                    # Save invoice items to Excel
                    customer_name = customer_search_var.get()
                    invoice_items_saved = save_invoice_items_to_excel(invoice_number, customer_name if customer_name and customer_name != "Select Customer" else "")

                    # Ask user if they want to update product quantities
                    update_quantities = messagebox.askyesno("Update Product Quantities",
                                                         "Do you want to update product quantities in inventory?\n\n"
                                                         "This will subtract the quantities used in this invoice from the product inventory.")

                    # Update product quantities if user confirms
                    quantities_updated = False
                    if update_quantities:
                        quantities_updated = update_product_quantities()

                    # Ask if user wants to open the file
                    success_message = f"Invoice saved to:\n{filepath}\n\n"
                    if invoice_items_saved:
                        success_message += f"Invoice items have been saved.\n\n"
                    if quantities_updated:
                        success_message += f"Product quantities have been updated in inventory.\n\n"
                    success_message += "Would you like to open the file?"

                    open_file = messagebox.askyesno("Success", success_message)

                    if open_file:
                        os.startfile(filepath)

                    # Ask if user wants to create a new invoice (refresh invoice number)
                    if messagebox.askyesno("New Invoice", "Do you want to create a new invoice with a fresh invoice number?"):
                        refresh_invoice_number()
                else:
                    raise Exception("Could not copy PDF to final location")
            else:
                raise Exception("Temporary PDF was not created successfully")

        finally:
            # Clean up temporary file
            if temp_pdf and os.path.exists(temp_pdf):
                try:
                    os.unlink(temp_pdf)
                except:
                    pass

    except PermissionError:
        messagebox.showerror("Error",
            "Could not save the PDF. Please ensure you have write permission to the folder D:\\AMSSoftX\\Softwares\\AMS-InvoSync\\Data\\Invoice")
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred while generating the PDF:\n{str(e)}")
        print(f"Debug - Error details: {str(e)}")  # For debugging

# Excel-style Entry widget class
class ExcelEntry(tk.Entry):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.configure(
            relief="solid",
            borderwidth=1,
            font=("Segoe UI", 9),
            bg="white",
            highlightthickness=1,
            highlightbackground="#d1d1d1",
            highlightcolor="#00a8ff"
        )

# Excel-style Text widget class
class ExcelText(tk.Text):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        self.configure(
            relief="solid",
            borderwidth=1,
            font=("Segoe UI", 9),
            bg="white",
            highlightthickness=1,
            highlightbackground="#d1d1d1",
            highlightcolor="#00a8ff"
        )

def generate_invoice_number():
    current_date = datetime.now()
    return f"INV{current_date.strftime('%Y%m%d%H%M%S')}"

root = tk.Tk()
root.title("AMS-InvoSync")
root.state("zoomed")
root.configure(bg="#f8f9fa")
root.withdraw()  # Hide the main window initially

# Modern Professional Color Scheme
HEADER_BG = "#1a237e"  # Deep Indigo
SIDEBAR_BG = "#283593"  # Indigo
SIDEBAR_ACTIVE = "#5c6bc0"  # Lighter Indigo
SIDEBAR_HOVER = "#3949ab"  # Medium Indigo
SIDEBAR_TEXT = "#ffffff"  # White text
CARD_BG = "#ffffff"  # White card background
CARD_BORDER = "#e0e0e0"  # Light gray border
ACCENT_COLOR = "#ff5722"  # Deep Orange accent
SUCCESS_COLOR = "#4caf50"  # Green
WARNING_COLOR = "#ff9800"  # Orange
DANGER_COLOR = "#f44336"  # Red
INFO_COLOR = "#2196f3"  # Blue
TEXT_PRIMARY = "#212121"  # Almost black
TEXT_SECONDARY = "#757575"  # Dark gray
BTN_BG = "#3f51b5"  # Primary button color
BTN_HOVER = "#303f9f"  # Darker primary color
BTN_FONT = ("Segoe UI", 11)
TITLE_FONT = ("Segoe UI", 14, "bold")

# Icons for sidebar (emoji-based for simplicity)
DASHBOARD_ICON = "📊"
INVOICE_ICON = "📝"
TRACKING_ICON = "🔍"
CUSTOMER_ICON = "👥"
PRODUCTS_ICON = "📦"
PROFILE_ICON = "🏢"
HELP_ICON = "❓"

# Function to create modern styled buttons
def create_modern_button(parent, text, command, bg_color=BTN_BG, fg_color="white", font=("Segoe UI", 10), width=None, height=None, icon=None):
    """Create a modern button with hover effect and optional icon"""
    # Create a frame to hold the button for better styling
    btn_frame = tk.Frame(parent, bg=parent.cget("bg"))

    # Create the actual button with modern styling
    button = tk.Button(btn_frame, text=text, bg=bg_color, fg=fg_color, font=font,
                     command=command, relief="flat", borderwidth=0, padx=15, pady=8)

    if width:
        button.config(width=width)
    if height:
        button.config(height=height)

    # Add icon if provided
    if icon:
        button.config(text=f"{icon} {text}")

    button.pack(fill="both", expand=True)

    # Add hover effects
    def on_enter(e):
        button['background'] = darken_color(bg_color)
        button.config(cursor="hand2")

    def on_leave(e):
        button['background'] = bg_color

    button.bind("<Enter>", on_enter)
    button.bind("<Leave>", on_leave)

    # Add a subtle shadow effect
    shadow = tk.Frame(btn_frame, height=2, bg="#dddddd")
    shadow.place(relx=0.02, rely=0.95, relwidth=0.96)

    return btn_frame

# Login window
login_window = None
signup_window = None
forgot_window = None

def show_login_window():
    global login_window, current_user, user_type, is_authenticated

    # Check if login window already exists
    if login_window is not None:
        login_window.destroy()

    # Create login window
    login_window = tk.Toplevel(root)
    login_window.title("AMS-InvoSync Login")
    login_window.geometry("400x500")
    login_window.resizable(False, False)
    login_window.configure(bg="white")
    login_window.protocol("WM_DELETE_WINDOW", root.quit)  # Close app if login window is closed

    # Set window icon if available
    try:
        if os.path.exists("icon.ico"):
            login_window.iconbitmap("icon.ico")
            print("Applied icon to login window")
    except Exception as e:
        print(f"Could not set icon for login window: {e}")

    # Center the window
    login_window.update_idletasks()
    width = login_window.winfo_width()
    height = login_window.winfo_height()
    x = (login_window.winfo_screenwidth() // 2) - (width // 2)
    y = (login_window.winfo_screenheight() // 2) - (height // 2)
    login_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    # Create login form
    header_frame = tk.Frame(login_window, bg=HEADER_BG, height=80)
    header_frame.pack(fill="x")

    # Welcome text
    tk.Label(header_frame, text="Welcome to AMS-InvoSync", font=("Segoe UI", 16, "bold"),
           bg=HEADER_BG, fg="white").pack(pady=(20, 5))

    # Main content frame
    content_frame = tk.Frame(login_window, bg="white")
    content_frame.pack(fill="both", expand=True, padx=40, pady=30)

    # User selection dropdown
    tk.Label(content_frame, text="Select User:", font=("Segoe UI", 12), bg="white").pack(anchor="w", pady=(0, 5))

    # Get available users
    users = get_available_users()

    # User dropdown
    user_var = tk.StringVar()

    # Check if we have users
    if not users:
        # No users found - show message and redirect to signup
        messagebox.showinfo("No Users Found", "No users found in the system. Please sign up first.")
        show_signup_window()
        return

    # Check if we're using default users (user1, user2)
    if users == ["user1", "user2"]:
        # Check if we should show signup instead
        if not os.path.exists(os.path.join(profile_dir, "setup_complete.flag")):
            print("Using default users but no setup flag found - showing signup")
            messagebox.showinfo("Setup Required", "Please complete the initial setup.")
            show_signup_window()
            return

    # Set default value
    user_var.set(users[0])

    # Create dropdown with users
    user_dropdown = ttk.Combobox(content_frame, textvariable=user_var, values=users, font=("Segoe UI", 11), state="readonly")
    user_dropdown.pack(fill="x", pady=(0, 15))

    # Print debug info
    print(f"Login dropdown showing users: {users}")

    # Password field
    tk.Label(content_frame, text="Password:", font=("Segoe UI", 12), bg="white").pack(anchor="w", pady=(0, 5))
    password_entry = tk.Entry(content_frame, show="•", font=("Segoe UI", 11))
    password_entry.pack(fill="x", pady=(0, 20))

    # Login button
    login_btn = tk.Button(content_frame, text="LOGIN", bg=BTN_BG, fg="white", font=("Segoe UI", 14, "bold"),
                        command=lambda: login(user_var.get(), password_entry.get()), cursor="hand2",
                        activebackground=BTN_HOVER, activeforeground="white", bd=0, padx=10, pady=10)
    login_btn.pack(fill="x", pady=(10, 20))

    # Forgot password link
    forgot_frame = tk.Frame(content_frame, bg="white")
    forgot_frame.pack(fill="x")

    forgot_label = tk.Label(forgot_frame, text="Forgot Password?", font=("Segoe UI", 10),
                          fg="#3498db", bg="white", cursor="hand2")
    forgot_label.pack(side="left")
    forgot_label.bind("<Button-1>", lambda e: show_forgot_password())

    # No signup option on login page - signup is only shown on first run

    # Function to login
    def login(username, password):
        global current_user, user_type, is_authenticated

        if not username or not password:
            messagebox.showerror("Login Error", "Please enter both username and password")
            return

        # Verify credentials
        success, user_role = verify_login(username, password)

        if success:
            current_user = username
            user_type = user_role
            is_authenticated = True

            # Close login window
            login_window.destroy()

            # Show main window in full screen
            root.deiconify()
            root.state("zoomed")  # Ensure it's maximized

            # DO NOT create users sheet here - it would overwrite custom users
            # with default ones

            # Show welcome message
            messagebox.showinfo("Welcome", f"Welcome, {username}!")

            # Apply user permissions
            apply_user_permissions(user_role)

            # Show Dashboard by default
            show_frame("Dashboard")
        else:
            messagebox.showerror("Login Error", "Invalid username or password")

def show_signup_window():
    global signup_window

    # Check if signup window already exists
    if signup_window is not None:
        signup_window.destroy()

    # Create signup window
    signup_window = tk.Toplevel(root)
    signup_window.title("AMS-InvoSync Sign Up")
    signup_window.geometry("400x650")  # Increased height to fit all content
    signup_window.resizable(False, False)
    signup_window.configure(bg="white")

    # Set window icon if available
    try:
        if os.path.exists("icon.ico"):
            signup_window.iconbitmap("icon.ico")
            print("Applied icon to signup window")
    except Exception as e:
        print(f"Could not set icon for signup window: {e}")

    # Center the window
    signup_window.update_idletasks()
    width = signup_window.winfo_width()
    height = signup_window.winfo_height()
    x = (signup_window.winfo_screenwidth() // 2) - (width // 2)
    y = (signup_window.winfo_screenheight() // 2) - (height // 2)
    signup_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    # Create signup form
    header_frame = tk.Frame(signup_window, bg=HEADER_BG, height=80)
    header_frame.pack(fill="x")

    # Sign Up text
    tk.Label(header_frame, text="Sign Up for AMS-InvoSync", font=("Segoe UI", 16, "bold"),
           bg=HEADER_BG, fg="white").pack(pady=(20, 5))

    # Main content frame - reduced vertical padding to fit more content
    content_frame = tk.Frame(signup_window, bg="white")
    content_frame.pack(fill="both", expand=True, padx=40, pady=(20, 10))

    # User 1 section
    tk.Label(content_frame, text="User 1 (Admin)", font=("Segoe UI", 12, "bold"), bg="white").pack(anchor="w", pady=(0, 5))

    # Username field
    tk.Label(content_frame, text="Username:", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user1_username = tk.Entry(content_frame, font=("Segoe UI", 11))
    user1_username.pack(fill="x", pady=(0, 5))

    # Password field
    tk.Label(content_frame, text="Password:", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user1_password = tk.Entry(content_frame, show="•", font=("Segoe UI", 11))
    user1_password.pack(fill="x", pady=(0, 5))

    # Secret Number field
    tk.Label(content_frame, text="Secret Number (for password recovery):", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user1_secret = tk.Entry(content_frame, font=("Segoe UI", 11))
    user1_secret.pack(fill="x", pady=(0, 10))

    # User 2 section - added separator for visual clarity
    separator = tk.Frame(content_frame, height=1, bg="#e0e0e0")
    separator.pack(fill="x", pady=5)

    tk.Label(content_frame, text="User 2 (Limited Access)", font=("Segoe UI", 12, "bold"), bg="white").pack(anchor="w", pady=(5, 5))

    # Username field
    tk.Label(content_frame, text="Username:", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user2_username = tk.Entry(content_frame, font=("Segoe UI", 11))
    user2_username.pack(fill="x", pady=(0, 5))

    # Password field
    tk.Label(content_frame, text="Password:", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user2_password = tk.Entry(content_frame, show="•", font=("Segoe UI", 11))
    user2_password.pack(fill="x", pady=(0, 5))

    # Secret Number field
    tk.Label(content_frame, text="Secret Number (for password recovery):", font=("Segoe UI", 11), bg="white").pack(anchor="w", pady=(0, 2))
    user2_secret = tk.Entry(content_frame, font=("Segoe UI", 11))
    user2_secret.pack(fill="x", pady=(0, 10))

    # Add some space before the button
    tk.Frame(content_frame, height=10, bg="white").pack(fill="x")

    # Sign Up button - made larger and more prominent
    signup_btn = tk.Button(content_frame, text="SIGN UP", bg=BTN_BG, fg="white", font=("Segoe UI", 14, "bold"),
                         command=lambda: signup(
                             user1_username.get(), user1_password.get(), user1_secret.get(),
                             user2_username.get(), user2_password.get(), user2_secret.get()
                         ), cursor="hand2",
                         activebackground=BTN_HOVER, activeforeground="white", bd=0, padx=10, pady=10)
    signup_btn.pack(fill="x", pady=(10, 20))

    # Back to login link
    back_frame = tk.Frame(content_frame, bg="white")
    back_frame.pack(fill="x")

    back_label = tk.Label(back_frame, text="Back to Login", font=("Segoe UI", 10),
                        fg="#3498db", bg="white", cursor="hand2")
    back_label.pack(side="left")
    back_label.bind("<Button-1>", lambda e: back_to_login())

    # Function to sign up
    def signup(user1_name, user1_pass, user1_secret, user2_name, user2_pass, user2_secret):
        # Validate inputs
        if not user1_name or not user1_pass or not user1_secret:
            messagebox.showerror("Sign Up Error", "Please fill all fields for User 1")
            return

        if not user2_name or not user2_pass or not user2_secret:
            messagebox.showerror("Sign Up Error", "Please fill all fields for User 2")
            return

        # Create users
        success1, message1 = create_user(user1_name, user1_pass, "admin", user1_secret)
        success2, message2 = create_user(user2_name, user2_pass, "limited", user2_secret)

        if success1 and success2:
            # Create the setup complete flag file to indicate first-time setup is done
            flag_file = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile", "setup_complete.flag")
            try:
                with open(flag_file, 'w') as f:
                    f.write("Setup completed")
            except Exception as e:
                print(f"Error creating flag file: {e}")

            messagebox.showinfo("Sign Up Success", "Users created successfully! You can now login.")
            back_to_login()
        else:
            messagebox.showerror("Sign Up Error", f"Error creating users:\nUser 1: {message1}\nUser 2: {message2}")

    # Function to go back to login
    def back_to_login():
        signup_window.destroy()
        show_login_window()

def show_forgot_password():
    global forgot_window

    # Check if forgot window already exists
    if forgot_window is not None:
        forgot_window.destroy()

    # Create forgot password window
    forgot_window = tk.Toplevel(root)
    forgot_window.title("AMS-InvoSync Forgot Password")
    forgot_window.geometry("400x400")  # Increased height to ensure button is visible
    forgot_window.resizable(False, False)
    forgot_window.configure(bg="white")

    # Set window icon if available
    try:
        if os.path.exists("icon.ico"):
            forgot_window.iconbitmap("icon.ico")
            print("Applied icon to forgot password window")
    except Exception as e:
        print(f"Could not set icon for forgot password window: {e}")

    # Center the window
    forgot_window.update_idletasks()
    width = forgot_window.winfo_width()
    height = forgot_window.winfo_height()
    x = (forgot_window.winfo_screenwidth() // 2) - (width // 2)
    y = (forgot_window.winfo_screenheight() // 2) - (height // 2)
    forgot_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    # Create forgot password form
    header_frame = tk.Frame(forgot_window, bg=HEADER_BG, height=80)
    header_frame.pack(fill="x")

    # Forgot Password text
    tk.Label(header_frame, text="Reset Password", font=("Segoe UI", 16, "bold"),
           bg=HEADER_BG, fg="white").pack(pady=(20, 5))

    # Main content frame - reduced padding to fit all elements
    content_frame = tk.Frame(forgot_window, bg="white")
    content_frame.pack(fill="both", expand=True, padx=40, pady=20)

    # Username field
    tk.Label(content_frame, text="Username:", font=("Segoe UI", 12), bg="white").pack(anchor="w", pady=(0, 5))
    username_entry = tk.Entry(content_frame, font=("Segoe UI", 11))
    username_entry.pack(fill="x", pady=(0, 15))

    # Secret Number field
    tk.Label(content_frame, text="Secret Number:", font=("Segoe UI", 12), bg="white").pack(anchor="w", pady=(0, 5))
    secret_entry = tk.Entry(content_frame, font=("Segoe UI", 11))
    secret_entry.pack(fill="x", pady=(0, 15))

    # New Password field
    tk.Label(content_frame, text="New Password:", font=("Segoe UI", 12), bg="white").pack(anchor="w", pady=(0, 5))
    new_password_entry = tk.Entry(content_frame, show="•", font=("Segoe UI", 11))
    new_password_entry.pack(fill="x", pady=(0, 20))

    # Reset button - made even larger and more prominent with bright color
    reset_btn = tk.Button(content_frame, text="UPDATE PASSWORD", bg="#FF5722", fg="white", font=("Segoe UI", 16, "bold"),
                        command=lambda: reset_pwd(username_entry.get(), secret_entry.get(), new_password_entry.get()),
                        cursor="hand2", activebackground="#E64A19", activeforeground="white", bd=0, padx=15, pady=15,
                        height=2)  # Added height to make button taller
    reset_btn.pack(fill="x", pady=(15, 20))

    # Back to login link
    back_frame = tk.Frame(content_frame, bg="white")
    back_frame.pack(fill="x")

    back_label = tk.Label(back_frame, text="Back to Login", font=("Segoe UI", 10),
                        fg="#3498db", bg="white", cursor="hand2")
    back_label.pack(side="left")
    back_label.bind("<Button-1>", lambda e: back_to_login())

    # Function to reset password
    def reset_pwd(username, secret, new_password):
        if not username or not secret or not new_password:
            messagebox.showerror("Reset Error", "Please fill all fields")
            return

        # Reset password
        success = reset_password(username, secret, new_password)

        if success:
            messagebox.showinfo("Reset Success", "Password reset successfully! You can now login with your new password.")
            back_to_login()
        else:
            messagebox.showerror("Reset Error", "Invalid username or secret number")

    # Function to go back to login
    def back_to_login():
        forgot_window.destroy()
        show_login_window()

def get_available_users():
    # Use company_profile.xlsx with hidden Users sheet
    profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
    profile_file = os.path.join(profile_dir, "company_profile.xlsx")

    # For debugging
    print(f"Looking for users in file: {profile_file}")
    print(f"File exists: {os.path.exists(profile_file)}")

    if not os.path.exists(profile_file):
        print("Profile file doesn't exist")
        return []

    try:
        # Load workbook
        wb = load_workbook(profile_file)
        print(f"Available sheets in workbook: {wb.sheetnames}")

        # Check if Users sheet exists
        if "Users" not in wb.sheetnames:
            print("Users sheet doesn't exist in profile file")
            return []

        # Get Users sheet
        users_sheet = wb["Users"]
        print(f"Users sheet max row: {users_sheet.max_row}")

        # Get usernames
        usernames = []
        for row in range(2, users_sheet.max_row + 1):
            username = users_sheet.cell(row=row, column=1).value
            if username:
                usernames.append(username)
                print(f"Found username: {username}")

        if usernames:
            print(f"Found usernames in profile Excel: {usernames}")
            return usernames
        else:
            print("No usernames found in profile Excel")
            return []
    except Exception as e:
        print(f"Error getting available users from profile Excel: {e}")
        return []

def apply_user_permissions(user_role):
    # This function will be called after successful login
    # It will disable certain features based on user role
    global user_type, limited_user

    # Store the user type globally
    user_type = user_role

    # Set limited_user flag for use throughout the application
    limited_user = (user_role == "limited")

    print(f"Applying permissions for user role: {user_role}")

    # Force refresh all frames to apply permissions
    refresh_all_frames()

    # Apply permissions to all frames immediately
    if limited_user:
        print("Applying limited user permissions to all frames")
        for frame_name, frame in frames.items():
            apply_limited_permissions(frame, frame_name)
    else:
        print("Applying admin permissions to all frames")
        for frame_name, frame in frames.items():
            enable_all_buttons_in_frame(frame)

# Function to apply limited user permissions to a frame
def apply_limited_permissions(frame, frame_name):
    print(f"Applying limited permissions to {frame_name} frame")

    # Process all widgets in this frame
    for widget in frame.winfo_children():
        apply_limited_permissions_recursive(widget, frame_name)

# Function to enable all buttons in a specific frame
def enable_all_buttons_in_frame(frame):
    # Process all widgets in this frame
    for widget in frame.winfo_children():
        enable_buttons_recursive(widget)

# Function to recursively apply limited permissions to widgets
def apply_limited_permissions_recursive(widget, parent_frame):
    # Check if this is a button that should be disabled
    if isinstance(widget, tk.Button):
        button_text = widget.cget("text").lower() if hasattr(widget, "cget") else ""

        # Apply specific permissions based on parent frame and button text
        if parent_frame == "Profile":
            # Disable ALL buttons in Profile for limited users
            print(f"Disabling profile button: {button_text}")
            widget.config(state="disabled")

        elif parent_frame == "Products":
            # For Products: Allow Add, disable Edit and Delete
            if any(keyword in button_text for keyword in ["edit", "delete", "update", "clear"]):
                print(f"Disabling product button: {button_text}")
                widget.config(state="disabled")

        elif parent_frame == "Customer":
            # For Customer: Allow Add, disable Edit and Delete
            if any(keyword in button_text for keyword in ["edit", "delete", "update", "clear"]):
                print(f"Disabling customer button: {button_text}")
                widget.config(state="disabled")

        elif parent_frame == "Invoice Tracking":
            # Disable Pay button in Invoice Tracking
            if "pay" in button_text:
                print(f"Disabling pay button: {button_text}")
                widget.config(state="disabled")

        elif parent_frame == "Invoice":
            # Allow basic invoice operations but disable payment-related buttons
            if any(keyword in button_text for keyword in ["payment", "received"]):
                print(f"Disabling invoice payment button: {button_text}")
                widget.config(state="disabled")

    # For TreeView widgets, we need to handle them specially
    elif isinstance(widget, ttk.Treeview):
        # Disable double-click editing for limited users
        if parent_frame in ["Products", "Customer"]:
            # Unbind double-click events for limited users
            widget.unbind("<Double-1>")
            print(f"Disabled double-click editing for {parent_frame} treeview")

    # For Entry widgets, disable them in certain contexts
    elif isinstance(widget, tk.Entry) or isinstance(widget, ttk.Entry) or isinstance(widget, ExcelEntry):
        if parent_frame == "Products" and hasattr(widget, "name") and widget.name in ["edit", "delete"]:
            widget.config(state="disabled")
            print(f"Disabled product entry: {widget.name}")
        elif parent_frame == "Customer" and hasattr(widget, "name") and widget.name in ["edit", "delete"]:
            widget.config(state="disabled")
            print(f"Disabled customer entry: {widget.name}")

    # Process all children of this widget
    for child in widget.winfo_children() if hasattr(widget, "winfo_children") else []:
        apply_limited_permissions_recursive(child, parent_frame)

# Function to recursively enable buttons in widgets
def enable_buttons_recursive(widget):
    # Check if this is a button that should be enabled
    if isinstance(widget, tk.Button) and widget.cget("state") == "disabled":
        widget.config(state="normal")

    # Process all children of this widget
    for child in widget.winfo_children() if hasattr(widget, "winfo_children") else []:
        enable_buttons_recursive(child)

# Function to refresh all frames to apply permissions
def refresh_all_frames():
    # Force refresh of all frames
    for frame_name in frames:
        if frame_name == "Dashboard":
            # Clear any existing widgets in the dashboard frame
            for widget in frames[frame_name].winfo_children():
                widget.destroy()
            create_dashboard()  # Create fresh dashboard content
        elif frame_name == "Invoice":
            # Clear any existing widgets in the invoice frame
            for widget in frames[frame_name].winfo_children():
                widget.destroy()
            create_invoice_content()
        elif frame_name == "Invoice Tracking":
            # Update all invoice statuses based on the current system date
            update_all_invoice_statuses()
            # Create invoice tracking content
            create_invoice_tracking_content()
        elif frame_name == "Support & Help":
            # Create support and help content
            create_support_help_content()
        elif frame_name == "Customer":
            # Load customers
            load_customers()
            update_customer_preview()
        elif frame_name == "Products":
            # Load products
            load_products()
            update_product_preview()

# Header and sidebar setup

# Modern elevated header with shadow effect
header = tk.Frame(root, bg=HEADER_BG, height=60)
header.pack(side="top", fill="x")

# Create a shadow effect below the header
header_shadow = tk.Frame(root, height=2, bg="#dddddd")
header_shadow.pack(side="top", fill="x")

# Left side - App name with modern font
app_name_frame = tk.Frame(header, bg=HEADER_BG)
app_name_frame.pack(side="left", padx=20, pady=10)

app_logo = tk.Label(app_name_frame, text="AMS", bg=HEADER_BG, fg=ACCENT_COLOR,
                  font=("Segoe UI", 20, "bold"))
app_logo.pack(side="left")

app_name = tk.Label(app_name_frame, text="-InvoSync", bg=HEADER_BG, fg="white",
                  font=("Segoe UI", 20, "bold"))
app_name.pack(side="left")

# Right side - Developer info and website with modern styling
right_header = tk.Frame(header, bg=HEADER_BG)
right_header.pack(side="right", padx=20, pady=10)

# Developer info with icon
dev_frame = tk.Frame(right_header, bg=HEADER_BG)
dev_frame.pack(side="top", anchor="e", pady=(0, 5))

dev_icon = tk.Label(dev_frame, text="👨‍💻", bg=HEADER_BG, fg="white",
                  font=("Segoe UI", 12))
dev_icon.pack(side="left", padx=(0, 5))

dev_label = tk.Label(dev_frame, text="Developed by AMSSoftX", bg=HEADER_BG, fg="white",
                   font=("Segoe UI", 10))
dev_label.pack(side="left")

# Website with modern styling and hover effect
website_frame = tk.Frame(right_header, bg=HEADER_BG)
website_frame.pack(side="top", anchor="e")

web_icon = tk.Label(website_frame, text="🌐", bg=HEADER_BG, fg="white",
                  font=("Segoe UI", 12))
web_icon.pack(side="left", padx=(0, 5))

website_label = tk.Label(website_frame, text="www.amssoftx.com", bg=HEADER_BG, fg=INFO_COLOR,
                       font=("Segoe UI", 10, "underline"), cursor="hand2")
website_label.pack(side="left")

def on_website_enter(e):
    website_label.config(fg="#90caf9")  # Lighter blue on hover

def on_website_leave(e):
    website_label.config(fg=INFO_COLOR)

def on_website_click(e):
    import webbrowser
    webbrowser.open("https://amssoftx.com")

website_label.bind("<Enter>", on_website_enter)
website_label.bind("<Leave>", on_website_leave)
website_label.bind("<Button-1>", on_website_click)

# Modern sidebar with icons and better spacing
sidebar = tk.Frame(root, bg=SIDEBAR_BG, width=220)
sidebar.pack(side="left", fill="y")
# Make sure sidebar maintains its width
sidebar.pack_propagate(False)

# Add a logo/branding area at the top of sidebar
sidebar_header = tk.Frame(sidebar, bg=SIDEBAR_BG, height=60)
sidebar_header.pack(fill="x")

# Add a separator between header and menu items
sidebar_separator = tk.Frame(sidebar, height=1, bg="#3949ab")
sidebar_separator.pack(fill="x", padx=10)

# Define tabs with their icons
tab_data = [
    {"name": "Dashboard", "icon": DASHBOARD_ICON},
    {"name": "Invoice", "icon": INVOICE_ICON},
    {"name": "Invoice Tracking", "icon": TRACKING_ICON},
    {"name": "Customer", "icon": CUSTOMER_ICON},
    {"name": "Products", "icon": PRODUCTS_ICON},
    {"name": "Profile", "icon": PROFILE_ICON},
    {"name": "Support & Help", "icon": HELP_ICON}
]

# Extract just the tab names for compatibility with existing code
tabs = [tab["name"] for tab in tab_data]

# Create main area for content with modern light background
main_area = tk.Frame(root, bg="#f8f9fa")  # Lighter background for better contrast
main_area.pack(side="right", fill="both", expand=True)

# Add a subtle shadow to the left edge of the main area for depth
main_shadow = tk.Frame(main_area, width=2, bg="#eeeeee")
main_shadow.pack(side="left", fill="y")

# Create frames for each tab
frames = {}
for tab in tabs:
    frame = ttk.Frame(main_area)
    frames[tab] = frame

def create_dashboard():
    # Create main dashboard frame
    dashboard_frame = frames["Dashboard"]
    dashboard_frame.pack_forget()  # Clear existing content

    # Create a container frame to control width
    container = tk.Frame(dashboard_frame, bg="#f8f9fa")
    container.pack(fill="both", expand=True)

    # Create scrollable canvas for dashboard
    canvas = tk.Canvas(container, bg="#f8f9fa", highlightthickness=0)
    scrollbar = tk.Scrollbar(container, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="#f8f9fa")

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # Pack the canvas and scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Main content frame to control width
    main_content = tk.Frame(scrollable_frame, bg="#f8f9fa")
    main_content.pack(fill="both", expand=True, padx=20, pady=20)

    # Modern welcome header with accent color
    title_frame = tk.Frame(main_content, bg="#f8f9fa")
    title_frame.pack(fill="x", pady=(10, 30))

    # Welcome text with accent color
    welcome_label = tk.Label(title_frame, text="Welcome to ", font=("Segoe UI", 24),
                           bg="#f8f9fa", fg=TEXT_PRIMARY)
    welcome_label.pack(side="left")

    app_name_label = tk.Label(title_frame, text="AMS", font=("Segoe UI", 24, "bold"),
                            bg="#f8f9fa", fg=ACCENT_COLOR)
    app_name_label.pack(side="left")

    app_name_suffix = tk.Label(title_frame, text="-InvoSync", font=("Segoe UI", 24, "bold"),
                             bg="#f8f9fa", fg=BTN_BG)
    app_name_suffix.pack(side="left")

    # Profile Information Frame - modern card design
    profile_frame = tk.Frame(main_content, bg=CARD_BG, relief="solid", borderwidth=1)
    profile_frame.pack(fill="x", padx=20, pady=10)

    # Add a subtle shadow effect
    shadow_frame = tk.Frame(main_content, height=5, bg="#eeeeee")
    shadow_frame.pack(fill="x", padx=25, pady=(0, 20))

    # Load profile data
    profile_file = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile", "company_profile.xlsx")
    if os.path.exists(profile_file):
        try:
            df = pd.read_excel(profile_file)
            if not df.empty:
                profile_data = df.iloc[0].to_dict()

                # Top section with logo and company name
                top_section = tk.Frame(profile_frame, bg="white")
                top_section.pack(fill="x", padx=20, pady=10)

                # Left side - Logo
                logo_frame = tk.Frame(top_section, bg="white")
                logo_frame.pack(side="left", padx=(0,20))

                if "Company Logo" in profile_data and os.path.exists(profile_data["Company Logo"]):
                    try:
                        img = Image.open(profile_data["Company Logo"])
                        img = img.resize((100, 100))  # Smaller size for dashboard
                        logo_img = ImageTk.PhotoImage(img)
                        logo_label = tk.Label(logo_frame, image=logo_img, bg="white")
                        logo_label.image = logo_img
                        logo_label.pack()
                    except:
                        tk.Label(logo_frame, text="Logo", font=("Segoe UI", 12), bg="white", width=10, height=5).pack()

                # Right side - Company name and details
                details_frame = tk.Frame(top_section, bg="white")
                details_frame.pack(side="left", fill="x", expand=True)

                # Company Name - Larger and bold
                if "Company Name" in profile_data:
                    tk.Label(details_frame, text=profile_data["Company Name"],
                            font=("Segoe UI", 16, "bold"), bg="white").pack(anchor="w")

                # Details in smaller font
                details = [
                    ("GST Number:", profile_data.get("GST Number", "N/A")),
                    ("Email:", profile_data.get("Email", "N/A")),
                    ("Phone:", profile_data.get("Phone Number", "N/A")),
                    ("Address:", profile_data.get("Address", "N/A"))
                ]

                for label, value in details:
                    detail_frame = tk.Frame(details_frame, bg="white")
                    detail_frame.pack(fill="x", pady=2)
                    tk.Label(detail_frame, text=label, font=("Segoe UI", 9, "bold"),
                            bg="white", fg="#2f3640", width=10, anchor="w").pack(side="left")
                    tk.Label(detail_frame, text=value, font=("Segoe UI", 9),
                            bg="white", anchor="w").pack(side="left", padx=5)
        except Exception as e:
            tk.Label(profile_frame, text="Error loading profile data",
                    font=("Segoe UI", 10), bg="white", fg="red").pack(pady=10)
    else:
        tk.Label(profile_frame, text="Please set up your company profile in the Profile tab",
                font=("Segoe UI", 10), bg="white").pack(pady=10)

    # Company info removed from bottom right as it's now in the header

    # Make sure the dashboard frame is visible
    dashboard_frame.pack(fill="both", expand=True)

def show_frame(name):
    # Hide all frames first
    for frame in frames.values():
        frame.pack_forget()

    # Check if leaving product management while editing
    global product_edit_index, customer_edit_index
    current_tab = None
    for tab, frame in frames.items():
        if frame.winfo_viewable():
            current_tab = tab
            break

    if current_tab == "Products" and product_edit_index is not None:
        if messagebox.askyesno("Unsaved Changes", "You have unsaved product changes. Do you want to save them?"):
            add_or_update_product()
        else:
            product_edit_index = None
            clear_form()
            add_button.config(text="➕ Add Item")

    # Check if leaving customer management while editing
    elif current_tab == "Customer" and customer_edit_index is not None:
        if messagebox.askyesno("Unsaved Changes", "You have unsaved customer changes. Do you want to save them?"):
            add_or_update_customer()
        else:
            customer_edit_index = None
            clear_customer_form()
            add_button.config(text="➕ Add Customer")

    # Show the selected frame
    if name == "Dashboard":
        # Clear any existing widgets in the dashboard frame
        for widget in frames[name].winfo_children():
            widget.destroy()
        create_dashboard()  # Create fresh dashboard content
    else:
        frames[name].pack(fill="both", expand=True)

    # Load specific data based on the frame
    if name == "Invoice":
        # Update all invoice statuses based on the current system date
        update_all_invoice_statuses()

        # Clear any existing widgets in the invoice frame
        for widget in frames["Invoice"].winfo_children():
            widget.destroy()
        create_invoice_content()
    elif name == "Invoice Tracking":
        # Update all invoice statuses based on the current system date
        update_all_invoice_statuses()

        # Create invoice tracking content
        create_invoice_tracking_content()
    elif name == "Support & Help":
        # Create support and help content
        create_support_help_content()
    elif name == "Customer":
        print("=== SWITCHING TO CUSTOMER TAB ===")

        # Reset customer edit index to ensure we're in "add" mode
        customer_edit_index = None

        # Load customers when switching to Customer tab
        load_customers()

        # Make sure the customer form is visible for all users
        try:
            customer_form.pack(fill="x", pady=(0, 10))
            print("Customer form packed successfully")
        except Exception as e:
            print(f"Error packing customer form: {str(e)}")

        # For limited users, show preview but disable edit/delete functionality
        if limited_user:
            # Make sure preview is visible for limited users too
            try:
                customers_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
                print("Customer preview frame packed for limited user")
            except Exception as e:
                print(f"Error packing customer preview frame: {str(e)}")

        # Make sure Add and Clear buttons are enabled for ALL users
        try:
            add_button.config(state="normal")
            clear_button.config(state="normal")
            add_button.config(text="➕ Add Customer")  # Reset button text
            print("Add and Clear buttons enabled")
        except Exception as e:
            print(f"Error enabling Add/Clear buttons: {str(e)}")

        # Print debug info
        print("Customer tab selected - Add button state:", add_button.cget("state"))
        print("Customer tab selected - Clear button state:", clear_button.cget("state"))

        # Clear the form to start fresh
        try:
            clear_customer_form()
            print("Customer form cleared")
        except Exception as e:
            print(f"Error clearing customer form: {str(e)}")

        # Update the preview for all users
        try:
            update_customer_preview()
            print("Customer preview updated")
        except Exception as e:
            print(f"Error updating customer preview: {str(e)}")

        # Force update the UI
        root.update_idletasks()

    elif name == "Products":
        # Load products when switching to Products tab
        load_products()

        # For limited users, show preview but disable edit/delete functionality
        if limited_user:
            # Make sure preview is visible for limited users too
            products_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)

            # Make sure Add and Clear buttons are enabled for limited users
            for widget in products_form.winfo_children():
                if isinstance(widget, tk.Button) and ("Add" in widget.cget("text") or "Clear" in widget.cget("text")):
                    widget.config(state="normal")

        # Update the preview for all users
        update_product_preview()

    # Apply user permissions to the newly shown frame
    if user_type == "limited":
        # Disable edit buttons in the current frame based on specific permissions
        print(f"Applying limited user permissions to {name} frame")
        apply_limited_permissions(frames[name], name)
    else:
        # Enable all buttons for admin users
        print(f"Applying admin permissions to {name} frame")
        enable_all_buttons_in_frame(frames[name])

def create_invoice_content():
    global invoice_number, invoice_date, invoice_items, amount_words_value
    global subtotal_label, discount_label, sgst_label, cgst_label, grand_total_label, received_label, balance_label
    global received_entry, payment_date  # Add these variables to global scope

    # Create main layout for invoice with scrolling
    main_invoice_canvas = tk.Canvas(frames["Invoice"], bg="white")
    main_invoice_scrollbar = tk.Scrollbar(frames["Invoice"], orient="vertical", command=main_invoice_canvas.yview)
    main_invoice_content = tk.Frame(main_invoice_canvas, bg="white")

    # Generate invoice number and date
    global invoice_number, invoice_date, invoice_items
    if invoice_number is None:
        invoice_number = generate_invoice_number()
    if invoice_date is None:
        invoice_date = datetime.now().strftime("%d-%m-%Y")
    invoice_items.clear()  # Clear any existing items

    # Check user permissions for limited users
    global user_type

    # Configure the canvas
    main_invoice_canvas.create_window((0, 0), window=main_invoice_content, anchor="nw")
    main_invoice_content.bind("<Configure>", lambda e: main_invoice_canvas.configure(scrollregion=main_invoice_canvas.bbox("all")))
    main_invoice_canvas.configure(yscrollcommand=main_invoice_scrollbar.set)

    # Pack the main scrollable components
    main_invoice_canvas.pack(side="left", fill="both", expand=True)
    main_invoice_scrollbar.pack(side="right", fill="y")

    # Create main layout for invoice content
    invoice_layout = tk.Frame(main_invoice_content, bg="white")
    invoice_layout.pack(fill="both", expand=True, padx=15, pady=10)

    # Upper Header with Invoice Title and Details
    upper_header = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    upper_header.pack(fill="x", pady=(0, 10))

    # TAX INVOICE title
    title_frame = tk.Frame(upper_header, bg="white")
    title_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(title_frame, text="INVOICE", font=("Segoe UI", 16, "bold"),
            bg="white").pack(side="left")

    # Invoice number and date
    details_frame = tk.Frame(upper_header, bg="white")
    details_frame.pack(fill="x", padx=10, pady=5)

    # Create a frame for invoice number with refresh button
    invoice_number_frame = tk.Frame(details_frame, bg="white")
    invoice_number_frame.pack(side="left", padx=20)

    # Invoice number label
    invoice_number_label = tk.Label(invoice_number_frame, text=f"Invoice No: {invoice_number}",
            font=("Segoe UI", 10, "bold"), bg="white")
    invoice_number_label.pack(side="left")

    # Refresh button for invoice number
    refresh_btn = tk.Button(invoice_number_frame, text="🔄", bg="#3498db", fg="white",
                          font=("Segoe UI", 8), command=lambda: refresh_invoice_number(),
                          cursor="hand2", width=2, height=1)
    refresh_btn.pack(side="left", padx=5)

    # Date label
    tk.Label(details_frame, text=f"Date: {invoice_date}",
            font=("Segoe UI", 10), bg="white").pack(side="right", padx=20)

    # Company Details Section
    company_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    company_frame.pack(fill="x", pady=(0, 10))

    company_header = tk.Frame(company_frame, bg="#2f3640", height=25)
    company_header.pack(fill="x")
    tk.Label(company_header, text="Company Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    company_content = tk.Frame(company_frame, bg="white")
    company_content.pack(fill="x", padx=10, pady=5)

    # Display company logo from profile
    if "Company Logo" in preview_data and os.path.exists(preview_data["Company Logo"]):
        try:
            img = Image.open(preview_data["Company Logo"])
            img = img.resize((80, 80))
            logo = ImageTk.PhotoImage(img)
            logo_label = tk.Label(company_content, image=logo, bg="white")
            logo_label.image = logo
            logo_label.pack(side="left", padx=10)
        except Exception as e:
            print(f"Error loading company logo: {e}")

    # Company details in two columns
    company_left = tk.Frame(company_content, bg="white")
    company_left.pack(side="left", fill="x", expand=True, padx=5)

    company_right = tk.Frame(company_content, bg="white")
    company_right.pack(side="left", fill="x", expand=True, padx=5)

    # Left column fields
    company_fields_left = [
        ("Company Name", preview_data.get("Company Name", "")),
        ("Email", preview_data.get("Email", "")),
        ("Phone Number", preview_data.get("Phone Number", ""))
    ]

    # Right column fields
    company_fields_right = [
        ("GST Number", preview_data.get("GST Number", "")),
        ("State", preview_data.get("State", "")),
        ("Address", preview_data.get("Address", ""))
    ]

    for label, value in company_fields_left:
        frame = tk.Frame(company_left, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        tk.Label(frame, text=value, font=("Segoe UI", 9),
                bg="white").pack(side="left", padx=5)

    for label, value in company_fields_right:
        frame = tk.Frame(company_right, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        tk.Label(frame, text=value, font=("Segoe UI", 9),
                bg="white").pack(side="left", padx=5)

    # Customer Details Section
    customer_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    customer_frame.pack(fill="x", pady=(0, 10))

    customer_header = tk.Frame(customer_frame, bg="#2f3640", height=25)
    customer_header.pack(fill="x")
    tk.Label(customer_header, text="Customer Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    customer_content = tk.Frame(customer_frame, bg="white")
    customer_content.pack(fill="x", padx=10, pady=5)

    # Customer search dropdown
    customer_search_frame = tk.Frame(customer_content, bg="white")
    customer_search_frame.pack(fill="x", pady=5)

    tk.Label(customer_search_frame, text="Select Customer:", font=("Segoe UI", 9, "bold"),
            bg="white").pack(side="left", padx=(0, 5))

    global customer_search_var
    customer_search_var = tk.StringVar()
    customer_search_var.set("Select Customer")

    customer_dropdown = ttk.Combobox(customer_search_frame, textvariable=customer_search_var,
                                   font=("Segoe UI", 9), width=30)
    customer_names = [c["Customer Name"] for c in customers_data]
    customer_dropdown['values'] = ["Select Customer"] + customer_names
    customer_dropdown.pack(side="left")

    # Customer details display
    customer_details_frame = tk.Frame(customer_content, bg="white")
    customer_details_frame.pack(fill="x", pady=5)

    # Left column for customer details
    customer_left = tk.Frame(customer_details_frame, bg="white")
    customer_left.pack(side="left", fill="x", expand=True, padx=5)

    # Right column for customer details
    customer_right = tk.Frame(customer_details_frame, bg="white")
    customer_right.pack(side="left", fill="x", expand=True, padx=5)

    # Create entry fields for customer details
    global customer_entries_invoice
    customer_entries_invoice = {}

    # Left column fields
    left_fields = [
        ("Customer Name", "entry"),
        ("Email", "entry"),
        ("Phone Number", "entry"),
        ("GST Number", "entry"),
        ("State", "entry")
    ]

    # Right column fields
    right_fields = [
        ("Address", "text"),
        ("Shipping To", "text")
    ]

    # Create left column fields with increased width
    for field, field_type in left_fields:
        frame = tk.Frame(customer_left, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{field}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        entry = ExcelEntry(frame, width=40, font=("Segoe UI", 9))
        entry.pack(side="left", padx=5)
        customer_entries_invoice[field] = entry

    # Create right column fields with increased width
    for field, field_type in right_fields:
        frame = tk.Frame(customer_right, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{field}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        entry = ExcelText(frame, height=3, width=40, font=("Segoe UI", 9))
        entry.pack(side="left", padx=5)
        customer_entries_invoice[field] = entry

    def update_customer_fields(*args):
        customer_name = customer_search_var.get()
        if customer_name and customer_name != "Select Customer":
            customer = next((c for c in customers_data if c["Customer Name"] == customer_name), None)
            if customer:
                for field in customer_entries_invoice:
                    if isinstance(customer_entries_invoice[field], tk.Text):
                        customer_entries_invoice[field].delete("1.0", "end")
                        customer_entries_invoice[field].insert("1.0", customer.get(field, ""))
                    else:
                        customer_entries_invoice[field].delete(0, "end")
                        customer_entries_invoice[field].insert(0, customer.get(field, ""))

    customer_search_var.trace("w", update_customer_fields)

    # Products Table Section
    products_table_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    products_table_frame.pack(fill="both", expand=True, pady=(0, 10))

    # Table container with fixed width
    table_container = tk.Frame(products_table_frame, bg="white")
    table_container.pack(fill="both", expand=True, padx=10, pady=5)

    # Define column widths and headers
    headers = ["Sr.", "Item Name", "Quantity", "Unit", "Price/Unit", "Discount", "GST", "Amount", "Actions"]
    widths = [40, 300, 50, 50, 70, 50, 50, 70, 80]  # Pixel widths

    # Create header row
    header_frame = tk.Frame(table_container, bg="white")
    header_frame.pack(fill="x", pady=(0, 1))

    # Create header cells
    for i, (header, width) in enumerate(zip(headers, widths)):
        header_cell = tk.Frame(header_frame, width=width, height=30, bg="#f0f0f0", bd=1, relief="solid")
        header_cell.pack_propagate(False)
        header_cell.grid(row=0, column=i, sticky="nsew")

        label = tk.Label(header_cell, text=header, bg="#f0f0f0", font=("Segoe UI", 9, "bold"))
        label.pack(expand=True)

    # Table content frame
    table_frame = tk.Frame(table_container, bg="white")
    table_frame.pack(fill="both", expand=True)

    invoice_items = []

    def remove_product_row(row_frame):
        row_frame.destroy()
        invoice_items[:] = [item for item in invoice_items if item['frame'] != row_frame]
        update_totals()

    def add_product_row():
        global invoice_items

        # Create a frame for this row
        row_frame = tk.Frame(table_frame, bg="white")
        row_frame.pack(fill="x", pady=1)

        # Sr. No.
        sr_no = len(invoice_items) + 1
        sr_cell = tk.Frame(row_frame, width=widths[0], height=30, bg="white", bd=1, relief="solid")
        sr_cell.pack_propagate(False)
        sr_cell.grid(row=0, column=0, sticky="nsew")
        tk.Label(sr_cell, text=str(sr_no), bg="white").pack(expand=True)

        # Item Name with auto-suggestion
        name_cell = tk.Frame(row_frame, width=widths[1], height=30, bg="white", bd=1, relief="solid")
        name_cell.pack_propagate(False)
        name_cell.grid(row=0, column=1, sticky="nsew")
        item_name = ttk.Combobox(name_cell, font=("Segoe UI", 9))
        item_name['values'] = [p["Product Name"] for p in products_data]
        item_name.pack(expand=True, fill="both", padx=5)

        # Function to handle product selection
        def on_product_select(event):
            selected_name = item_name.get()
            selected_product = next((p for p in products_data if p["Product Name"] == selected_name), None)
            if selected_product:
                # Auto-fill only price and GST
                price.delete(0, "end")
                price.insert(0, selected_product.get("Rate", ""))
                gst.delete(0, "end")
                gst.insert(0, selected_product.get("Tax %", ""))
                calculate_amount()

        item_name.bind('<<ComboboxSelected>>', on_product_select)

        # Quantity
        qty_cell = tk.Frame(row_frame, width=widths[2], height=30, bg="white", bd=1, relief="solid")
        qty_cell.pack_propagate(False)
        qty_cell.grid(row=0, column=2, sticky="nsew")
        quantity = ExcelEntry(qty_cell, justify="right")
        quantity.pack(expand=True, fill="both", padx=5)

        # Unit (Simple Entry field without suggestions)
        unit_cell = tk.Frame(row_frame, width=widths[3], height=30, bg="white", bd=1, relief="solid")
        unit_cell.pack_propagate(False)
        unit_cell.grid(row=0, column=3, sticky="nsew")
        unit = ExcelEntry(unit_cell)
        unit.pack(expand=True, fill="both", padx=5)

        # Price/Unit
        price_cell = tk.Frame(row_frame, width=widths[4], height=30, bg="white", bd=1, relief="solid")
        price_cell.pack_propagate(False)
        price_cell.grid(row=0, column=4, sticky="nsew")
        price = ExcelEntry(price_cell, justify="right")
        price.pack(expand=True, fill="both", padx=5)

        # Discount
        disc_cell = tk.Frame(row_frame, width=widths[5], height=30, bg="white", bd=1, relief="solid")
        disc_cell.pack_propagate(False)
        disc_cell.grid(row=0, column=5, sticky="nsew")
        discount = ExcelEntry(disc_cell, justify="right")
        discount.pack(expand=True, fill="both", padx=5)

        # GST%
        gst_cell = tk.Frame(row_frame, width=widths[6], height=30, bg="white", bd=1, relief="solid")
        gst_cell.pack_propagate(False)
        gst_cell.grid(row=0, column=6, sticky="nsew")
        gst = ExcelEntry(gst_cell, justify="right")
        gst.pack(expand=True, fill="both", padx=5)

        # Amount
        amount_cell = tk.Frame(row_frame, width=widths[7], height=30, bg="white", bd=1, relief="solid")
        amount_cell.pack_propagate(False)
        amount_cell.grid(row=0, column=7, sticky="nsew")
        amount_label = tk.Label(amount_cell, text="₹0.00", bg="white", font=("Segoe UI", 9))
        amount_label.pack(expand=True)

        # Delete button
        btn_cell = tk.Frame(row_frame, width=widths[8], height=30, bg="white", bd=1, relief="solid")
        btn_cell.pack_propagate(False)
        btn_cell.grid(row=0, column=8, sticky="nsew")
        delete_btn = tk.Button(btn_cell, text="🗑️", bg="#ff6b6b", fg="white",
                            command=lambda: remove_product_row(row_frame), bd=0)
        delete_btn.pack(expand=True, fill="both")

        def calculate_amount(*args):
            try:
                qty = float(quantity.get() or 0)
                price_per_unit = float(price.get() or 0)
                discount_percent = float(discount.get() or 0)
                gst_percent = float(gst.get() or 0)

                subtotal = qty * price_per_unit
                discount_amount = subtotal * (discount_percent / 100)
                amount_after_discount = subtotal - discount_amount
                gst_amount = amount_after_discount * (gst_percent / 100)
                total = amount_after_discount + gst_amount

                amount_label.config(text=f"₹{total:.2f}")
                update_totals()
            except ValueError:
                amount_label.config(text="₹0.00")

        for entry in [quantity, price, discount, gst]:
            entry.bind('<KeyRelease>', calculate_amount)

        invoice_items.append({
            'frame': row_frame,
            'item_name': item_name,
            'quantity': quantity,
            'unit': unit,
            'price': price,
            'discount': discount,
            'gst': gst,
            'amount': amount_label
        })

        update_totals()

    # Add Item button with proper styling
    add_item_btn = tk.Button(products_table_frame, text="➕ Add Item", bg=SIDEBAR_ACTIVE, fg="white",
                            font=("Segoe UI", 8, "bold"), command=add_product_row)
    add_item_btn.pack(pady=5)

    # Allow add item button for limited users
    # Limited users can add items but can't save the invoice

    # Totals Section
    totals_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    totals_frame.pack(fill="x", pady=(0, 10))

    totals_content = tk.Frame(totals_frame, bg="white")
    totals_content.pack(fill="x", padx=10, pady=5)

    # Remove the upper amount in words label
    # amount_words_label = tk.Label(totals_content, text="Amount in Words: ", font=("Segoe UI", 9), bg="white")
    # amount_words_label.pack(anchor="w")

    # Payment Details Frame
    payment_frame = tk.Frame(totals_content, bg="white")
    payment_frame.pack(fill="x", pady=5)

    # Left side - Payment input fields
    payment_left = tk.Frame(payment_frame, bg="white")
    payment_left.pack(side="left", fill="x", expand=True)

    # Received Amount with entry
    received_frame = tk.Frame(payment_left, bg="white")
    received_frame.pack(fill="x", pady=2)
    tk.Label(received_frame, text="Received Amount: ₹", font=("Segoe UI", 9, "bold"),
            bg="white").pack(side="left")
    received_entry = ExcelEntry(received_frame, width=15)
    received_entry.pack(side="left")

    # Payment Date with DateEntry
    date_frame = tk.Frame(payment_left, bg="white")
    date_frame.pack(fill="x", pady=2)
    tk.Label(date_frame, text="Payment Date:", font=("Segoe UI", 9, "bold"),
            bg="white").pack(side="left")
    payment_date = DateEntry(date_frame, width=15, background='darkblue',
                        foreground='white', borderwidth=2, date_pattern='dd-mm-yyyy',
                        font=("Segoe UI", 9))
    payment_date.pack(side="left", padx=5)

    # Right side - Totals
    totals_right = tk.Frame(payment_frame, bg="white")
    totals_right.pack(side="right", fill="x")

    subtotal_label = tk.Label(totals_right, text="Sub Total: ₹0.00",
            font=("Segoe UI", 9), bg="white")
    subtotal_label.pack(anchor="e")

    discount_label = tk.Label(totals_right, text="Discount: ₹0.00",
            font=("Segoe UI", 9), bg="white")
    discount_label.pack(anchor="e")

    sgst_label = tk.Label(totals_right, text="SGST: ₹0.00",
            font=("Segoe UI", 9), bg="white")
    sgst_label.pack(anchor="e")

    cgst_label = tk.Label(totals_right, text="CGST: ₹0.00",
            font=("Segoe UI", 9), bg="white")
    cgst_label.pack(anchor="e")

    grand_total_label = tk.Label(totals_right, text="Grand Total: ₹0.00",
            font=("Segoe UI", 10, "bold"), bg="white")
    grand_total_label.pack(anchor="e")

    received_label = tk.Label(totals_right, text="Received: ₹0.00",
            font=("Segoe UI", 9), bg="white")
    received_label.pack(anchor="e")

    balance_label = tk.Label(totals_right, text="Pending Amount: ₹0.00",
            font=("Segoe UI", 9, "bold"), fg="#e74c3c", bg="white")
    balance_label.pack(anchor="e")

    # Amount in words with better formatting (keep this one)
    amount_words_frame = tk.Frame(totals_content, bg="white")
    amount_words_frame.pack(fill="x", pady=5)
    amount_words_label = tk.Label(amount_words_frame, text="Amount in Words:",
                                font=("Segoe UI", 9, "bold"), bg="white")
    amount_words_label.pack(anchor="w")
    amount_words_value = tk.Label(amount_words_frame, text="Zero Rupees Only",
                                font=("Segoe UI", 9), bg="white", wraplength=400,
                                justify="left")
    amount_words_value.pack(anchor="w", pady=(0, 5))

    def calculate_balance(*args):
        try:
            received = float(received_entry.get() or 0)
            grand_total_text = grand_total_label.cget("text").split(": ")[1]
            grand_total = float(grand_total_text.replace("₹", "").replace(",", ""))

            # If received amount is zero, pending amount should be zero
            if received == 0:
                balance = 0
            else:
                # Otherwise, subtract received from grand total
                balance = grand_total - received

            received_label.config(text=f"Received: ₹{received:.2f}")
            balance_label.config(text=f"Pending Amount: ₹{balance:.2f}")

            # Change balance color based on amount
            if balance <= 0:
                balance_label.config(fg="#2ecc71")  # Green for fully paid
            else:
                balance_label.config(fg="#e74c3c")  # Red for pending
        except ValueError:
            received_label.config(text="Received: ₹0.00")
            balance_label.config(text="Pending Amount: ₹0.00")

    # Bind received amount entry to calculate balance
    received_entry.bind('<KeyRelease>', calculate_balance)

    def update_totals():
        subtotal = 0
        total_discount = 0
        total_gst = 0

        for item in invoice_items:
            try:
                qty = float(item['quantity'].get() or 0)
                price_per_unit = float(item['price'].get() or 0)
                discount_percent = float(item['discount'].get() or 0)
                gst_percent = float(item['gst'].get() or 0)

                item_subtotal = qty * price_per_unit
                item_discount = item_subtotal * (discount_percent / 100)
                item_amount_after_discount = item_subtotal - item_discount
                item_gst = item_amount_after_discount * (gst_percent / 100)

                subtotal += item_subtotal
                total_discount += item_discount
                total_gst += item_gst
            except ValueError:
                continue

        grand_total = subtotal - total_discount + total_gst
        sgst = total_gst / 2
        cgst = total_gst / 2

        # Update labels with clean formatting (no currency symbol, no black squares)
        subtotal_label.config(text=f"Sub Total: {subtotal:.2f}")
        discount_label.config(text=f"Discount: {total_discount:.2f}")
        sgst_label.config(text=f"SGST: {sgst:.2f}")
        cgst_label.config(text=f"CGST: {cgst:.2f}")
        grand_total_label.config(text=f"Grand Total: {grand_total:.2f}")

        # Update amount in words with better formatting
        try:
            amount_int = int(grand_total)
            # Fix decimal part calculation to ensure accuracy
            amount_decimal = round((grand_total - amount_int) * 100)
            amount_words = num2words(amount_int, lang='en_IN').title()

            if amount_decimal > 0:
                decimal_words = num2words(amount_decimal, lang='en_IN').title()
                amount_text = f"{amount_words} Rupees and {decimal_words} Paise Only"
            else:
                amount_text = f"{amount_words} Rupees Only"

            amount_words_value.config(text=amount_text)
        except Exception as e:
            print(f"Error in amount words calculation: {e}")
            amount_words_value.config(text="Zero Rupees Only")

        # Recalculate balance
        calculate_balance()

    # Bank Details Section
    bank_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    bank_frame.pack(fill="x", pady=(0, 10))

    bank_header = tk.Frame(bank_frame, bg="#2f3640", height=25)
    bank_header.pack(fill="x")
    tk.Label(bank_header, text="Bank Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    bank_content = tk.Frame(bank_frame, bg="white")
    bank_content.pack(fill="x", padx=10, pady=5)

    # Display bank details from profile
    bank_details = [
        f"Account Holder: {preview_data.get('Account Holder Name', '')}",
        f"Account Number: {preview_data.get('Account Number', '')}",
        f"IFSC Code: {preview_data.get('IFSC Code', '')}",
        f"Branch: {preview_data.get('Branch', '')}"
    ]

    for detail in bank_details:
        tk.Label(bank_content, text=detail, font=("Segoe UI", 9), bg="white").pack(anchor="w")

    # Terms and Conditions Section
    terms_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    terms_frame.pack(fill="x", pady=(0, 10))

    terms_header = tk.Frame(terms_frame, bg="#2f3640", height=25)
    terms_header.pack(fill="x")
    tk.Label(terms_header, text="Terms and Conditions", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    terms_content = tk.Frame(terms_frame, bg="white")
    terms_content.pack(fill="x", padx=10, pady=5)

    # Display terms from profile
    terms = preview_data.get("Terms and Conditions", default_terms).split('\n')
    for term in terms:
        tk.Label(terms_content, text=term, font=("Segoe UI", 9), bg="white").pack(anchor="w")

    # Company Seal & Sign Section
    seal_frame = tk.Frame(invoice_layout, bg="white", bd=1, relief="solid")
    seal_frame.pack(fill="x", pady=(0, 10))

    seal_header = tk.Frame(seal_frame, bg="#2f3640", height=25)
    seal_header.pack(fill="x")
    tk.Label(seal_header, text="Company Seal and Sign", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    seal_content = tk.Frame(seal_frame, bg="white")
    seal_content.pack(fill="x", padx=10, pady=5)

    # Create right-aligned frame for signature
    sign_frame = tk.Frame(seal_content, bg="white")
    sign_frame.pack(side="right", padx=20, pady=5)

    # Add company name and "For" text first
    tk.Label(sign_frame, text="For", font=("Segoe UI", 9),
            bg="white").pack(anchor="e")
    tk.Label(sign_frame, text=preview_data.get("Company Name", ""),
            font=("Segoe UI", 10, "bold"), bg="white").pack(anchor="e")

    # Display company seal/sign from profile below the text
    if "Stamp/Sign" in preview_data and os.path.exists(preview_data["Stamp/Sign"]):
        try:
            img = Image.open(preview_data["Stamp/Sign"])
            img = img.resize((120, 120))
            seal = ImageTk.PhotoImage(img)
            seal_label = tk.Label(sign_frame, image=seal, bg="white")
            seal_label.image = seal
            seal_label.pack(pady=(10, 5))
        except Exception as e:
            print(f"Error loading stamp/sign: {e}")

    # Add "Company Seal and Sign" text at the bottom
    tk.Label(sign_frame, text="Company Seal and Sign",
            font=("Segoe UI", 9), bg="white").pack(anchor="e", pady=(5, 0))

    # Action Buttons - modern card style
    buttons_frame = tk.Frame(invoice_layout, bg=CARD_BG, bd=1, relief="solid")
    buttons_frame.pack(fill="x", pady=10)

    # Add a title to the buttons section
    buttons_title = tk.Frame(buttons_frame, bg=HEADER_BG, height=30)
    buttons_title.pack(fill="x")
    tk.Label(buttons_title, text="Invoice Actions", font=("Segoe UI", 10, "bold"),
           bg=HEADER_BG, fg="white").pack(padx=10, pady=3)

    # Container for buttons with padding
    buttons_container = tk.Frame(buttons_frame, bg=CARD_BG, padx=10, pady=10)
    buttons_container.pack(fill="x")

    # Add save button with modern styling
    save_btn = create_modern_button(buttons_container, text="Save Invoice", command=save_invoice_to_excel,
                                  bg_color=BTN_BG, fg_color="white", icon="💾")
    save_btn.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    # Add refresh button with modern styling
    refresh_btn = create_modern_button(buttons_container, text="Refresh", command=refresh_invoice_number,
                                     bg_color=INFO_COLOR, fg_color="white", icon="🔄")
    refresh_btn.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    # Add download button with modern styling
    download_btn = create_modern_button(buttons_container, text="Download Invoice", command=generate_pdf,
                                      bg_color=SUCCESS_COLOR, fg_color="white", icon="⬇️")
    download_btn.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    # Add a subtle shadow effect
    shadow_frame = tk.Frame(invoice_layout, height=5, bg="#eeeeee")
    shadow_frame.pack(fill="x", padx=5, pady=(0, 10))

    # Allow save, refresh, and download buttons for all users
    # Limited users should be able to save and refresh invoices

    # Bind mouse wheel to canvas for better scrolling
    def _on_mousewheel(event):
        main_invoice_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    main_invoice_canvas.bind_all("<MouseWheel>", _on_mousewheel)

    # Update the canvas scroll region whenever the content changes
    def update_scroll_region(*args):
        main_invoice_canvas.configure(scrollregion=main_invoice_canvas.bbox("all"))

    main_invoice_content.bind("<Configure>", update_scroll_region)

# Create tab buttons with icons and modern styling
for tab_info in tab_data:
    tab_name = tab_info["name"]
    tab_icon = tab_info["icon"]

    # Create a container frame for each tab button for better styling
    btn_frame = tk.Frame(sidebar, bg=SIDEBAR_BG)
    btn_frame.pack(fill="x", pady=1)

    # Create the button with icon and text
    btn = tk.Frame(btn_frame, bg=SIDEBAR_BG, cursor="hand2")
    btn.pack(fill="x", padx=5, pady=5)

    # Icon on the left
    icon_label = tk.Label(btn, text=tab_icon, bg=SIDEBAR_BG, fg=SIDEBAR_TEXT,
                        font=("Segoe UI", 14), padx=10, pady=8)
    icon_label.pack(side="left")

    # Text on the right
    text_label = tk.Label(btn, text=tab_name, bg=SIDEBAR_BG, fg=SIDEBAR_TEXT,
                        font=BTN_FONT, pady=8, anchor="w")
    text_label.pack(side="left", fill="x", expand=True)

    # Add a subtle indicator for the active tab
    indicator = tk.Frame(btn, width=4, bg=SIDEBAR_BG)
    indicator.pack(side="left", fill="y")

    # Store references to all elements that need styling on hover/click
    elements = [btn, icon_label, text_label, indicator]

    # Bind events to all elements
    for element in elements:
        element.bind("<Button-1>", lambda e, t=tab_name: show_frame(t))
        element.bind("<Enter>", lambda e, elems=elements: [elem.config(bg=SIDEBAR_HOVER) for elem in elems])
        element.bind("<Leave>", lambda e, elems=elements: [elem.config(bg=SIDEBAR_BG) for elem in elems])

    # Add a subtle separator after each button
    separator = tk.Frame(sidebar, height=1, bg="#3949ab")
    separator.pack(fill="x", padx=15)

# Show Dashboard by default
show_frame("Dashboard")

# -------- Profile Tab --------
profile_frame = frames["Profile"]
layout = tk.Frame(profile_frame, bg="white")
layout.pack(fill="both", expand=True, padx=30, pady=20)

# Title
tk.Label(layout, text="🧾 Company Profile", font=("Segoe UI", 15, "bold"), bg="white", anchor="w").pack(fill="x", pady=(0, 20))

form_frame = tk.Frame(layout, bg="white")
form_frame.pack(side="top", fill="x", padx=10)

left_column = tk.Frame(form_frame, bg="white")
left_column.pack(side="left", fill="both", expand=True, padx=10)

middle_column = tk.Frame(form_frame, bg="white")
middle_column.pack(side="left", fill="both", expand=True, padx=10)

right_column = tk.Frame(form_frame, bg="white")
right_column.pack(side="left", fill="both", expand=True, padx=10)

fields_left = [
    ("Company Name", "entry"),
    ("Email", "entry"),
    ("Phone Number", "entry"),
    ("GST Number", "entry"),
    ("State", "entry"),
    ("Address", "text")
]

bank_fields = [
    ("Account Holder Name", "entry"),
    ("Account Number", "entry"),
    ("IFSC Code", "entry"),
    ("Branch", "entry")
]

entries = {}
preview_data = {}
logo_path = ""
logo_img = None
stamp_path = ""
stamp_img = None

def create_field(parent, label_text, field_type):
    lbl = tk.Label(parent, text=label_text, font=("Segoe UI", 9, "bold"), bg="white", anchor="w")
    lbl.pack(anchor="w", pady=(4, 1), padx=4)

    if field_type == "entry":
        entry = ExcelEntry(parent, width=30)
        entry.pack(fill="x", padx=4, ipady=2)
    else:
        entry = ExcelText(parent, height=2, width=30)
        entry.pack(fill="x", padx=4)

    entries[label_text] = entry

for label, type_ in fields_left:
    create_field(left_column, label, type_)

for label, type_ in bank_fields:
    create_field(middle_column, label, type_)

# Logo upload
logo_label = tk.Label(right_column, text="", bg="white", fg="green", font=("Segoe UI", 8, "italic"))

def upload_logo():
    global logo_path
    file_path = filedialog.askopenfilename(filetypes=[("Image Files", "*.png *.jpg *.jpeg")])
    if file_path:
        logo_path = file_path
        logo_label.config(text=f"Logo Selected: {file_path.split('/')[-1]}")
        preview_data["Company Logo"] = file_path

tk.Label(right_column, text="Company Logo (for header)", font=("Segoe UI", 9, "bold"), bg="white", anchor="w").pack(anchor="w", padx=5)
tk.Button(right_column, text="Upload Logo", command=upload_logo, bg="#dcdde1", font=("Segoe UI", 9)).pack(anchor="w", padx=5)
logo_label.pack(anchor="w", padx=5, pady=(2, 8))

# Stamp/Sign upload
stamp_label = tk.Label(right_column, text="", bg="white", fg="green", font=("Segoe UI", 8, "italic"))

def upload_stamp():
    global stamp_path
    file_path = filedialog.askopenfilename(filetypes=[("Image Files", "*.png *.jpg *.jpeg")])
    if file_path:
        stamp_path = file_path
        stamp_label.config(text=f"Stamp/Sign Selected: {file_path.split('/')[-1]}")
        preview_data["Stamp/Sign"] = file_path

tk.Label(right_column, text="Stamp / Sign (for documents)", font=("Segoe UI", 9, "bold"), bg="white", anchor="w").pack(anchor="w", padx=5)
tk.Button(right_column, text="Upload Stamp/Sign", command=upload_stamp, bg="#dcdde1", font=("Segoe UI", 9)).pack(anchor="w", padx=5)
stamp_label.pack(anchor="w", padx=5, pady=(2, 8))

# Add Terms and Conditions section to profile page
terms_label = tk.Label(right_column, text="Terms and Conditions", font=("Segoe UI", 9, "bold"), bg="white", anchor="w")
terms_label.pack(anchor="w", pady=(10, 2), padx=5)

terms_text = ExcelText(right_column, height=6, width=30)
terms_text.pack(fill="x", padx=5, pady=2)

# Add default terms
default_terms = """1. Payment is due within 30 days of invoice date.
2. Interest will be charged at 2% per month on overdue amounts.
3. Goods once sold will not be taken back.
4. All disputes are subject to jurisdiction of local courts."""
terms_text.insert("1.0", default_terms)

button_frame = tk.Frame(layout, bg="white")
button_frame.pack(fill="x")
save_btn = create_modern_button(button_frame, text="Save Profile", command=lambda: save_profile(),
                              bg_color=BTN_BG, fg_color="white", icon="💾")
save_btn.pack(pady=12)

preview_frame = tk.Frame(profile_frame, bg="#f0f0f0", padx=30, pady=30)
preview_frame.place_forget()

def save_profile():
    # Get all profile data
    for label, widget in entries.items():
        value = widget.get("1.0", "end").strip() if isinstance(widget, ExcelText) else widget.get().strip()
        preview_data[label] = value

    # Save terms and conditions
    preview_data["Terms and Conditions"] = terms_text.get("1.0", "end").strip()

    # Update bank details
    update_bank_details()

    try:
        # Create directory if it doesn't exist
        profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
        os.makedirs(profile_dir, exist_ok=True)

        # Save to Excel file
        profile_file = os.path.join(profile_dir, "company_profile.xlsx")

        # Check if file exists and has a Users sheet that we need to preserve
        users_data = None
        if os.path.exists(profile_file):
            try:
                # Load existing workbook to preserve Users sheet
                existing_wb = load_workbook(profile_file)
                if "Users" in existing_wb.sheetnames:
                    print("Preserving existing Users sheet")
                    users_sheet = existing_wb["Users"]

                    # Store Users sheet data
                    users_data = []
                    for row in range(1, users_sheet.max_row + 1):
                        row_data = []
                        for col in range(1, 5):  # 4 columns: Username, Password, User Type, Secret Number
                            row_data.append(users_sheet.cell(row=row, column=col).value)
                        users_data.append(row_data)

                    print(f"Preserved {len(users_data)} rows of user data")
            except Exception as e:
                print(f"Error reading existing Users sheet: {e}")

        # Define thin border for cells
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Create DataFrame with profile data
        df = pd.DataFrame([preview_data])

        # Save to Excel with proper formatting
        with pd.ExcelWriter(profile_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Profile')

            # Get the workbook and worksheet
            wb = writer.book
            worksheet = writer.sheets['Profile']

            # Format headers
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            header_alignment = Alignment(horizontal='center')

            for cell in worksheet[1]:
                # Set header properties
                # Skip setting cell.value as it's already set by to_excel
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = thin_border

            # Set column widths
            column_widths = {
                'Company Name': 30,
                'Email': 30,
                'Phone Number': 20,
                'GST Number': 20,
                'State': 20,
                'Address': 40,
                'Account Holder Name': 25,
                'Account Number': 20,
                'IFSC Code': 15,
                'Branch': 20,
                'Terms and Conditions': 50
            }

            for col, width in column_widths.items():
                if col in df.columns:
                    worksheet.column_dimensions[get_column_letter(df.columns.get_loc(col) + 1)].width = width

            # Restore Users sheet if we had one
            if users_data:
                # Create Users sheet
                if "Users" not in wb.sheetnames:
                    users_sheet = wb.create_sheet("Users")
                else:
                    users_sheet = wb["Users"]

                # Clear any existing data
                for row in range(1, users_sheet.max_row + 1):
                    for col in range(1, 5):
                        users_sheet.cell(row=row, column=col).value = None

                # Restore the data
                for row_idx, row_data in enumerate(users_data, 1):
                    for col_idx, value in enumerate(row_data, 1):
                        users_sheet.cell(row=row_idx, column=col_idx).value = value

                # Hide the Users sheet
                users_sheet.sheet_state = 'hidden'
                print(f"Restored Users sheet with {len(users_data)} rows and set to hidden")

        # Create backup of the Excel file
        if os.path.exists(profile_file):
            backup_excel_file(profile_file)

        # Update preview
        form_frame.pack_forget()
        button_frame.pack_forget()
        preview_frame.place(relx=0.05, rely=0.05, relwidth=0.9, relheight=0.85)
        update_preview()

        messagebox.showinfo("Success", "Profile saved successfully!")

    except Exception as e:
        print(f"Error saving profile: {str(e)}")
        messagebox.showerror("Error", f"Failed to save profile: {str(e)}")

def load_profile():
    global preview_data
    profile_file = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile", "company_profile.xlsx")

    if os.path.exists(profile_file):
        try:
            df = pd.read_excel(profile_file)
            if not df.empty:
                preview_data = df.iloc[0].to_dict()

                # Update form fields
                for label, widget in entries.items():
                    if label in preview_data:
                        if isinstance(widget, ExcelText):
                            widget.delete("1.0", "end")
                            widget.insert("1.0", preview_data[label])
                        else:
                            widget.delete(0, "end")
                            widget.insert(0, preview_data[label])

                # Update terms and conditions
                if "Terms and Conditions" in preview_data:
                    terms_text.delete("1.0", "end")
                    terms_text.insert("1.0", preview_data["Terms and Conditions"])

                # Update preview
                update_preview()

        except Exception as e:
            print(f"Error loading profile: {e}")

# Load profile on startup
load_profile()

def update_preview():
    global logo_img, stamp_img
    for widget in preview_frame.winfo_children():
        widget.destroy()

    content_frame = tk.Frame(preview_frame, bg="#f0f0f0")
    content_frame.pack(anchor="nw", fill="both", expand=True)

    logo_frame = tk.Frame(content_frame, bg="#f0f0f0")
    logo_frame.pack(side="right", padx=20, anchor="n")

    if "Company Logo" in preview_data:
        try:
            img = Image.open(preview_data["Company Logo"])
            img = img.resize((120, 120))
            logo_img = ImageTk.PhotoImage(img)
            logo_label_img = tk.Label(logo_frame, image=logo_img, bg="#f0f0f0")
            logo_label_img.image = logo_img
            logo_label_img.pack()

            # Add logo label and delete button in a frame
            logo_label_frame = tk.Frame(logo_frame, bg="#f0f0f0")
            logo_label_frame.pack(pady=(2, 10))
            tk.Label(logo_label_frame, text="Company Logo", font=("Segoe UI", 9, "bold"),
                    bg="#f0f0f0").pack(side="left", padx=(0, 5))
            tk.Button(logo_label_frame, text="🗑️", command=delete_logo,
                     bg="#ff6b6b", fg="white", font=("Segoe UI", 8)).pack(side="left")

        except Exception as e:
            tk.Label(logo_frame, text="⚠️ Cannot load logo", bg="#f0f0f0", fg="red").pack()

    if "Stamp/Sign" in preview_data:
        try:
            img = Image.open(preview_data["Stamp/Sign"])
            img = img.resize((100, 100))
            stamp_img = ImageTk.PhotoImage(img)
            stamp_label_img = tk.Label(logo_frame, image=stamp_img, bg="#f0f0f0")
            stamp_label_img.image = stamp_img
            stamp_label_img.pack()

            # Add stamp label and delete button in a frame
            stamp_label_frame = tk.Frame(logo_frame, bg="#f0f0f0")
            stamp_label_frame.pack(pady=(2, 10))
            tk.Label(stamp_label_frame, text="Stamp / Sign", font=("Segoe UI", 9, "bold"),
                    bg="#f0f0f0").pack(side="left", padx=(0, 5))
            tk.Button(stamp_label_frame, text="🗑️", command=delete_stamp,
                     bg="#ff6b6b", fg="white", font=("Segoe UI", 8)).pack(side="left")

        except Exception as e:
            tk.Label(logo_frame, text="⚠️ Cannot load Stamp/Sign", bg="#f0f0f0", fg="red").pack()

    left_info = tk.Frame(content_frame, bg="#f0f0f0")
    left_info.pack(side="left", padx=30, anchor="n")

    right_info = tk.Frame(content_frame, bg="#f0f0f0")
    right_info.pack(side="left", padx=10, anchor="n")

    tk.Label(left_info, text="Company & Contact Details", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w")
    for key in ["Company Name", "Email", "Phone Number", "GST Number", "State", "Address"]:
        value = preview_data.get(key, "")
        tk.Label(left_info, text=f"{key}:", font=("Segoe UI", 9, "bold"), bg="#f0f0f0").pack(anchor="w")
        tk.Label(left_info, text=value, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w", pady=(0, 5))

    tk.Label(right_info, text="Bank Details", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w")
    for key in ["Account Holder Name", "Account Number", "IFSC Code", "Branch"]:
        value = preview_data.get(key, "")
        tk.Label(right_info, text=f"{key}:", font=("Segoe UI", 9, "bold"), bg="#f0f0f0").pack(anchor="w")
        tk.Label(right_info, text=value, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w", pady=(0, 5))

    tk.Button(preview_frame, text="✏️ Edit Profile", bg="#00a8ff", fg="white", font=("Segoe UI", 10, "bold"), command=edit_profile).pack(pady=20)

    # Add Terms and Conditions to preview
    terms_preview = tk.Frame(content_frame, bg="#f0f0f0")
    terms_preview.pack(fill="x", padx=30, pady=(20, 0))


    tk.Label(terms_preview, text="Terms and Conditions", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w")
    terms = preview_data.get("Terms and Conditions", default_terms).split('\n')
    for term in terms:
        tk.Label(terms_preview, text=term, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w")

    # Add company details to preview
    company_preview = tk.Frame(content_frame, bg="#f0f0f0")
    company_preview.pack(fill="x", padx=30, pady=(20, 0))

    tk.Label(company_preview, text="Company Details", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w")

    company_details = [
        f"Company Name: {preview_data.get('Company Name', '')}",
        f"Email: {preview_data.get('Email', '')}",
        f"Phone Number: {preview_data.get('Phone Number', '')}",
        f"GST Number: {preview_data.get('GST Number', '')}",
        f"State: {preview_data.get('State', '')}",
        f"Address: {preview_data.get('Address', '')}"
    ]

    for detail in company_details:
        tk.Label(company_preview, text=detail, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w")

def edit_profile():
    preview_frame.place_forget()
    form_frame.pack(side="top", fill="x", padx=10)
    button_frame.pack(fill="x")

    for label, widget in entries.items():
        value = preview_data.get(label, "")
        if isinstance(widget, ExcelText):
            widget.delete("1.0", "end")
            widget.insert("1.0", value)
        else:
            widget.delete(0, "end")
            widget.insert(0, value)

    # Load terms and conditions
    terms_text.delete("1.0", "end")
    terms_text.insert("1.0", preview_data.get("Terms and Conditions", default_terms))

# -------- Products Tab --------
products_frame = frames["Products"]
products_frame.pack_forget()

# Create main layout for products
products_layout = tk.Frame(products_frame, bg="white")
products_layout.pack(fill="both", expand=True, padx=15, pady=10)

# Title and Search Bar Frame
header_frame = tk.Frame(products_layout, bg="white")
header_frame.pack(fill="x", pady=(0, 10))

# Title
tk.Label(header_frame, text=" Product Management", font=("Segoe UI", 15, "bold"), bg="white", anchor="w").pack(side="left")

# Search Frame
search_frame = tk.Frame(header_frame, bg="white")
search_frame.pack(side="right", padx=10)

tk.Label(search_frame, text="Search Product:", font=("Segoe UI", 10, "bold"), bg="white").pack(side="left", padx=5)
product_search_var = tk.StringVar()
product_search = ExcelEntry(search_frame, width=30, textvariable=product_search_var)
product_search.pack(side="left", padx=5)
product_search.bind('<KeyRelease>', lambda e: update_product_preview())

# Form frame
products_form = tk.Frame(products_layout, bg="white")
products_form.pack(fill="x", pady=(0, 20))

# Create two rows for product fields
row1 = tk.Frame(products_form, bg="white")
row1.pack(fill="x", padx=10, pady=5)

row2 = tk.Frame(products_form, bg="white")
row2.pack(fill="x", padx=10, pady=5)

row3 = tk.Frame(products_form, bg="white")
row3.pack(fill="x", padx=10, pady=5)

# First row fields
row1_fields = [
    ("Product Name", 40),
    ("HSN Code", 30),
    ("Unit", 20)
]

# Second row fields
row2_fields = [
    ("Quantity", 20),
    ("Rate", 20),
    ("Tax %", 20),
    ("Low Stock Alert", 20)
]

# Third row fields for dates
row3_fields = [
    ("Manufacture Date", 20),
    ("Expiry Date", 20)
]

product_entries = {}
current_edit_index = None

# Create fields in first row
for i, (label, width) in enumerate(row1_fields):
    field_frame = tk.Frame(row1, bg="white")
    field_frame.pack(side="left", expand=True, fill="x", padx=5)

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    entry = ExcelEntry(field_frame, width=width)
    entry.pack(fill="x", pady=2)
    product_entries[label] = entry

# Create fields in second row
for i, (label, width) in enumerate(row2_fields):
    field_frame = tk.Frame(row2, bg="white")
    field_frame.pack(side="left", expand=True, fill="x", padx=5)

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    entry = ExcelEntry(field_frame, width=width)
    entry.pack(fill="x", pady=2)
    product_entries[label] = entry

# Create date fields in third row
for i, (label, width) in enumerate(row3_fields):
    field_frame = tk.Frame(row3, bg="white")
    field_frame.pack(side="left", expand=True, fill="x", padx=5)

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    # Use DateEntry for date fields
    date_entry = DateEntry(field_frame, width=width, background='darkblue',
                          foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
    date_entry.pack(fill="x", pady=2)
    product_entries[label] = date_entry

# Define clear_form function
def clear_form():
    global product_edit_index
    product_edit_index = None
    for widget in product_entries.values():
        if isinstance(widget, ExcelText):
            widget.delete("1.0", "end")
        else:
            widget.delete(0, "end")
    add_button.config(text="➕ Add Item")

# Create a separate frame for buttons with white background
buttons_frame = tk.Frame(products_form, bg="white")
buttons_frame.pack(fill="x", padx=5, pady=5)

# Add buttons with modern styling
add_button_frame = create_modern_button(buttons_frame, text="Add Item",
                                      command=lambda: add_or_update_product(),
                                      bg_color=SUCCESS_COLOR, fg_color="white", icon="➕")
add_button_frame.pack(side="left", padx=5, fill="x", expand=True)
add_button = add_button_frame.winfo_children()[0]  # Get the actual button for later reference

clear_button_frame = create_modern_button(buttons_frame, text="Clear",
                                        command=clear_form,
                                        bg_color=DANGER_COLOR, fg_color="white", icon="🔄")
clear_button_frame.pack(side="left", padx=5, fill="x", expand=True)
clear_button = clear_button_frame.winfo_children()[0]  # Get the actual button for later reference

# Add a separator line with minimal padding
separator = ttk.Separator(products_form, orient="horizontal")
separator.pack(fill="x", padx=5, pady=2)  # Reduced padding

# Preview section for products with border
products_preview_frame = tk.Frame(products_layout, bg="#f0f0f0", bd=1, relief="solid")
products_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)

# Create a colorful professional style for the Treeview
style = ttk.Style()
# Configure the main treeview style with proper borders
style.configure("Treeview",
                background="white",
                foreground="black",
                rowheight=30,
                fieldbackground="white",
                borderwidth=1)

# Add grid lines to the treeview
style.layout("Treeview",
             [('Treeview.treearea', {'sticky': 'nswe', 'border': '1'})])
style.configure("Treeview", borderwidth=1, relief="solid")
# Configure the heading style to look like Excel headers (original style)
style.configure("Treeview.Heading",
                background="#E0E0E0",  # Original light gray header
                foreground="black",
                relief="raised",
                font=("Segoe UI", 10, "bold"),
                borderwidth=1)
# Configure selection color to be more vibrant
style.map("Treeview",
          background=[("selected", "#6a89cc")],  # Lighter blue for selection
          foreground=[("selected", "white")])

# Create a Treeview for product preview with professional Excel-like styling and grid lines
product_preview = ttk.Treeview(products_preview_frame,
                              columns=("Sr", "Name", "HSN", "Unit", "Quantity", "Rate", "Tax", "MfgDate", "ExpDate", "Stock", "Actions"),
                              show="headings",
                              style="Treeview")
product_preview.heading("Sr", text="Sr. No.")
product_preview.heading("Name", text="Product Name")
product_preview.heading("HSN", text="HSN Code")
product_preview.heading("Unit", text="Unit")
product_preview.heading("Quantity", text="Quantity")
product_preview.heading("Rate", text="Rate")
product_preview.heading("Tax", text="Tax %")
product_preview.heading("MfgDate", text="Mfg Date")
product_preview.heading("ExpDate", text="Exp Date")
product_preview.heading("Stock", text="Stock Status")
product_preview.heading("Actions", text="Actions")

# Set column widths and alignment for Excel-like appearance - adjusted to fit within window
product_preview.column("Sr", width=50, anchor="center")
product_preview.column("Name", width=150)  # Reduced width for product names
product_preview.column("HSN", width=80, anchor="center")
product_preview.column("Unit", width=60, anchor="center")
product_preview.column("Quantity", width=70, anchor="center")
product_preview.column("Rate", width=70, anchor="center")
product_preview.column("Tax", width=60, anchor="center")
product_preview.column("MfgDate", width=80, anchor="center")
product_preview.column("ExpDate", width=80, anchor="center")
product_preview.column("Stock", width=80, anchor="center")
product_preview.column("Actions", width=100, anchor="center")

# Make sure the horizontal scrollbar is enabled
product_scrollbar_x = ttk.Scrollbar(products_preview_frame, orient="horizontal", command=product_preview.xview)
product_preview.configure(xscrollcommand=product_scrollbar_x.set)
product_scrollbar_x.pack(side="bottom", fill="x")

# Configure tags for alternating row colors with more vibrant colors
product_preview.tag_configure("oddrow", background="#ecf0f1")  # Light blue-gray
product_preview.tag_configure("evenrow", background="#f5f6fa")  # Very light lavender

# Configure tags for stock status colors - make them bold and larger
product_preview.tag_configure("low_stock", foreground="#FF0000", font=("Segoe UI", 10, "bold"))  # Red for low stock
product_preview.tag_configure("ok_stock", foreground="#008000", font=("Segoe UI", 10, "bold"))   # Green for OK stock
product_preview.tag_configure("invalid_stock", foreground="#FFA500", font=("Segoe UI", 10, "bold"))  # Yellow/Orange for invalid stock

# Add scrollbars to treeview
product_scrollbar = ttk.Scrollbar(products_preview_frame, orient="vertical", command=product_preview.yview)
product_preview.configure(yscrollcommand=product_scrollbar.set)

# Pack the treeview and scrollbars
product_preview.pack(side="left", fill="both", expand=True)
product_scrollbar.pack(side="right", fill="y")

# Bind double-click event to edit product
product_preview.bind("<Double-1>", lambda event: edit_selected_product(event))

# Data directory and file paths
data_dir = r"D:\AMSSoftX\Softwares\AMS-InvoSync\Data"
products_dir = os.path.join(data_dir, "Product Data")
customers_dir = os.path.join(data_dir, "Customer Data")
products_file = os.path.join(products_dir, "Products.xlsx")
customer_file = os.path.join(customers_dir, "Customer.xlsx")

# Ensure directories exist
os.makedirs(products_dir, exist_ok=True)
os.makedirs(customers_dir, exist_ok=True)

# Load existing products
products_data = []

def load_products():
    global products_data
    if os.path.exists(products_file):
        try:
            df = pd.read_excel(products_file)
            products_data = df.to_dict('records')
            update_product_preview()
        except Exception as e:
            print(f"Error loading products: {e}")

def save_products():
    if products_data:
        try:
            # Create a new DataFrame with properly organized columns
            df = pd.DataFrame()

            # Add serial numbers
            df['Sr. No.'] = range(1, len(products_data) + 1)

            # Add product details in correct order
            columns = ['Product Name', 'HSN Code', 'Unit', 'Quantity', 'Rate', 'Tax %', 'Low Stock Alert', 'Manufacture Date', 'Expiry Date']
            for col in columns:
                df[col] = [product.get(col, '') for product in products_data]

            # Save to Excel with proper formatting
            with pd.ExcelWriter(products_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Products')

                # Get the worksheet
                worksheet = writer.sheets['Products']

                # Format headers
                header_font = Font(bold=True)
                header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                header_alignment = Alignment(horizontal='center')

                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment

                # Set column widths
                column_widths = {
                    'Sr. No.': 8,
                    'Product Name': 30,
                    'HSN Code': 15,
                    'Unit': 10,
                    'Quantity': 12,
                    'Rate': 12,
                    'Tax %': 10,
                    'Low Stock Alert': 15,
                    'Manufacture Date': 15,
                    'Expiry Date': 15
                }

                for col, width in column_widths.items():
                    worksheet.column_dimensions[get_column_letter(df.columns.get_loc(col) + 1)].width = width

            # Create backup of the Excel file
            if os.path.exists(products_file):
                backup_excel_file(products_file)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save products: {str(e)}")

def add_or_update_product():
    global product_edit_index, limited_user

    # Check if user has limited access and is trying to update a product
    if limited_user and product_edit_index is not None:
        messagebox.showinfo("Limited Access", "You do not have permission to edit products.\nPlease contact an administrator.")
        return

    product = {}
    for label, widget in product_entries.items():
        value = widget.get("1.0", "end").strip() if isinstance(widget, ExcelText) else widget.get().strip()
        product[label] = value

    if product_edit_index is not None:
        products_data[product_edit_index] = product
        product_edit_index = None
        add_button.config(text="➕ Add Item")
    else:
        products_data.append(product)

    # Update preview immediately
    update_product_preview()

    # Save to Excel
    save_products()

    # Clear form
    clear_form()

def edit_selected_product(event):
    # Check if user has limited access
    global user_type, limited_user
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to edit products.\nPlease contact an administrator.")
        return

    # Get selected item
    selected_item = product_preview.focus()
    if not selected_item:
        return

    # Get the index of the selected item
    selected_index = product_preview.index(selected_item)
    if selected_index < len(products_data):
        edit_product(selected_index)

def update_product_preview():
    # Clear existing items
    for item in product_preview.get_children():
        product_preview.delete(item)

    search_text = product_search_var.get().lower()

    if not products_data:
        # If no products, just return (treeview will be empty)
        return

    # Check user permissions - show preview for all users
    global user_type, limited_user
    # Make sure preview is visible for all users
    products_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)

    # Add all products to the preview
    filtered_count = 0
    for i, product in enumerate(products_data):
        # Check if product matches search text
        if search_text and not any(search_text.lower() in str(value).lower() for value in product.values()):
            continue

        filtered_count += 1

        # Determine stock status and set appropriate tag
        stock_tag = None
        try:
            quantity = float(product.get("Quantity", 0))
            low_stock = float(product.get("Low Stock Alert", 0))
            if quantity <= low_stock:
                stock_status = "⚠️ Low"
                stock_tag = "low_stock"
            else:
                stock_status = "✅ OK"
                stock_tag = "ok_stock"
        except:
            stock_status = "❓ Invalid"
            stock_tag = "invalid_stock"

        # Add Sr. No. and Actions columns to match customer format
        values = [
            str(filtered_count),  # Sr. No.
            product.get("Product Name", ""),
            product.get("HSN Code", ""),
            product.get("Unit", ""),
            product.get("Quantity", ""),
            f"₹{product.get('Rate', '0')}",
            f"{product.get('Tax %', '0')}%",
            product.get("Manufacture Date", ""),
            product.get("Expiry Date", ""),
            stock_status,
            "🔵 Edit | 🔴 Delete"  # Actions column with colored icons
        ]

        # Add alternating row colors for Excel-like appearance
        tag = "evenrow" if i % 2 == 0 else "oddrow"
        item_id = product_preview.insert("", "end", values=values, tags=(tag, f"row_{i}"))

        # Apply color tag to the entire row
        if stock_tag:
            # Apply the tag to the entire row
            product_preview.item(item_id, tags=(tag, f"row_{i}", stock_tag))

    # Add right-click menu for edit and delete
    def show_context_menu(event):
        selected_item = product_preview.identify_row(event.y)
        if selected_item:
            product_preview.selection_set(selected_item)
            selected_index = product_preview.index(selected_item)

            # Create colorful context menu
            context_menu = tk.Menu(root, tearoff=0, bg="#f5f6fa", fg="#2c3e50",
                                  activebackground="#4a69bd", activeforeground="white")

            # Check if user has limited access
            global limited_user
            if limited_user:
                context_menu.add_command(label="🔵 Edit (Limited Access)", font=("Segoe UI", 9, "bold"),
                                      command=lambda: messagebox.showinfo("Limited Access",
                                                                        "You do not have permission to edit products.\nPlease contact an administrator."))
                context_menu.add_command(label="🔴 Delete (Limited Access)", font=("Segoe UI", 9, "bold"),
                                      command=lambda: messagebox.showinfo("Limited Access",
                                                                        "You do not have permission to delete products.\nPlease contact an administrator."))
            else:
                context_menu.add_command(label="🔵 Edit", font=("Segoe UI", 9, "bold"),
                                      command=lambda: edit_product(selected_index))
                context_menu.add_command(label="🔴 Delete", font=("Segoe UI", 9, "bold"),
                                      command=lambda: delete_product(selected_index))

            # Display context menu
            context_menu.post(event.x_root, event.y_root)

    # Bind right-click menu
    product_preview.bind("<Button-3>", show_context_menu)

    # Bind click on the Actions column
    def on_actions_click(event):
        region = product_preview.identify_region(event.x, event.y)
        if region == "cell":
            column = product_preview.identify_column(event.x)
            if product_preview.column(column, "id") == "Actions":
                item = product_preview.identify_row(event.y)
                if item:
                    selected_index = product_preview.index(item)
                    # Show a colorful popup menu
                    actions_menu = tk.Menu(root, tearoff=0, bg="#f5f6fa", fg="#2c3e50",
                                          activebackground="#4a69bd", activeforeground="white")

                    # Check if user has limited access
                    global limited_user
                    if limited_user:
                        actions_menu.add_command(label="🔵 Edit (Limited Access)", font=("Segoe UI", 9, "bold"),
                                              command=lambda: messagebox.showinfo("Limited Access",
                                                                                "You do not have permission to edit products.\nPlease contact an administrator."))
                        actions_menu.add_command(label="🔴 Delete (Limited Access)", font=("Segoe UI", 9, "bold"),
                                              command=lambda: messagebox.showinfo("Limited Access",
                                                                                "You do not have permission to delete products.\nPlease contact an administrator."))
                    else:
                        actions_menu.add_command(label="🔵 Edit", font=("Segoe UI", 9, "bold"),
                                              command=lambda: edit_product(selected_index))
                        actions_menu.add_command(label="🔴 Delete", font=("Segoe UI", 9, "bold"),
                                              command=lambda: delete_product(selected_index))

                    actions_menu.post(event.x_root, event.y_root)

    # Bind left-click for actions column
    product_preview.bind("<Button-1>", on_actions_click)

def edit_product(index):
    global product_edit_index, limited_user

    # Check if user has limited access
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to edit products.\nPlease contact an administrator.")
        return

    product_edit_index = index
    product = products_data[index]

    for label, widget in product_entries.items():
        value = product.get(label, "")
        if isinstance(widget, ExcelText):
            widget.delete("1.0", "end")
            widget.insert("1.0", value)
        else:
            widget.delete(0, "end")
            widget.insert(0, value)

    add_button.config(text="💾 Update Item")

def delete_product(index):
    # Check if user has limited access
    global user_type, limited_user
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to delete products.\nPlease contact an administrator.")
        return

    if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this product?"):
        try:
            # Remove product from data
            products_data.pop(index)

            # Save changes to Excel file
            if products_data:
                # Create a new DataFrame with properly organized columns
                df = pd.DataFrame()

                # Add serial numbers
                df['Sr. No.'] = range(1, len(products_data) + 1)

                # Add product details in correct order
                columns = ['Product Name', 'HSN Code', 'Unit', 'Quantity', 'Rate', 'Tax %', 'Low Stock Alert', 'Manufacture Date', 'Expiry Date']
                for col in columns:
                    df[col] = [product.get(col, '') for product in products_data]

                # Save to Excel with proper formatting
                with pd.ExcelWriter(products_file, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Products')

                    # Get the worksheet
                    worksheet = writer.sheets['Products']

                    # Format headers
                    header_font = Font(bold=True)
                    header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                    header_alignment = Alignment(horizontal='center')

                    for cell in worksheet[1]:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment

                    # Set column widths
                    column_widths = {
                        'Sr. No.': 8,
                        'Product Name': 30,
                        'HSN Code': 15,
                        'Unit': 10,
                        'Quantity': 12,
                        'Rate': 12,
                        'Tax %': 10,
                        'Low Stock Alert': 15,
                        'Manufacture Date': 15,
                        'Expiry Date': 15
                    }

                    for col, width in column_widths.items():
                        worksheet.column_dimensions[get_column_letter(df.columns.get_loc(col) + 1)].width = width
            else:
                # If no products left, create an empty Excel file
                df = pd.DataFrame(columns=['Sr. No.', 'Product Name', 'HSN Code', 'Unit',
                                         'Quantity', 'Rate', 'Tax %', 'Low Stock Alert',
                                         'Manufacture Date', 'Expiry Date'])
                df.to_excel(products_file, index=False)

            # Create backup of the Excel file
            if os.path.exists(products_file):
                backup_excel_file(products_file)

            # Update the preview
            update_product_preview()
            messagebox.showinfo("Success", "Product deleted successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete product: {str(e)}")

# Load products on startup
load_products()

# -------- Customer Tab --------
customer_frame = frames["Customer"]
customer_frame.pack_forget()

# Create main layout for customers
customer_layout = tk.Frame(customer_frame, bg="white")
customer_layout.pack(fill="both", expand=True, padx=15, pady=10)

# Title
tk.Label(customer_layout, text="👥 Customer Management", font=("Segoe UI", 15, "bold"), bg="white", anchor="w").pack(fill="x", pady=(0, 10))

# Define customer management functions
def load_customers():
    global customers_data
    if os.path.exists(customer_file):
        try:
            df = pd.read_excel(customer_file)
            customers_data = df.to_dict('records')
            update_customer_preview()
        except Exception as e:
            print(f"Error loading customers: {e}")
            customers_data = []
            update_customer_preview()

def save_customers():
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(customer_file), exist_ok=True)

        # Create a new workbook and select active sheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Customers"

        # Define headers
        headers = ["Sr. No.", "Customer Name", "Email", "Phone Number", "GST Number", "State", "Address", "Shipping To"]

        # Set column widths
        ws.column_dimensions['A'].width = 8   # Sr. No.
        ws.column_dimensions['B'].width = 30  # Customer Name
        ws.column_dimensions['C'].width = 35  # Email
        ws.column_dimensions['D'].width = 15  # Phone Number
        ws.column_dimensions['E'].width = 20  # GST Number
        ws.column_dimensions['F'].width = 15  # State
        ws.column_dimensions['G'].width = 40  # Address
        ws.column_dimensions['H'].width = 40  # Shipping To

        # Style for headers
        header_style = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

        # Style for data cells
        data_font = Font(name='Segoe UI', size=10)
        data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # Add borders
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            # Set header properties
            cell.value = header
            cell.font = header_style
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border

        # Write data
        for row, customer in enumerate(customers_data, 2):
            # Sr. No.
            ws.cell(row=row, column=1, value=row-1)

        # Customer details
            columns = [
            customer.get("Customer Name", ""),
            customer.get("Email", ""),
            customer.get("Phone Number", ""),
            customer.get("GST Number", ""),
            customer.get("State", ""),
            customer.get("Address", ""),
            customer.get("Shipping To", "")
        ]

            for col, value in enumerate(columns, 2):
                cell = ws.cell(row=row, column=col, value=value)
                cell.font = data_font
                cell.alignment = data_alignment
                cell.border = thin_border

        # Auto-filter
        ws.auto_filter.ref = f"A1:H{len(customers_data) + 1}"

        # Freeze panes
        ws.freeze_panes = 'A2'

        # Save the workbook
        wb.save(customer_file)

        # Create backup of the Excel file
        if os.path.exists(customer_file):
            backup_excel_file(customer_file)

        messagebox.showinfo("Success", "Customer data saved successfully!")
    except Exception as e:
        messagebox.showerror("Error", f"Error saving customer data: {e}")

def update_customer_preview():
    # Clear existing items
    for item in customer_preview.get_children():
        customer_preview.delete(item)

    # Add all customers to the preview
    for i, customer in enumerate(customers_data):
        values = [
            customer.get("Customer Name", ""),
            customer.get("GST Number", ""),
            customer.get("Phone Number", ""),
            customer.get("Email", ""),
            customer.get("Address", ""),
            customer.get("City", ""),
            customer.get("State", ""),
            customer.get("Pincode", "")
        ]
        customer_preview.insert("", "end", values=values, tags=(f"row_{i}",))

def edit_customer(index):
    global customer_edit_index, limited_user

    # Check if user has limited access
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to edit customers.\nPlease contact an administrator.")
        return

    customer_edit_index = index
    customer = customers_data[index]

    for label, widget in customer_entries.items():
        value = customer.get(label, "")
        if isinstance(widget, ExcelText):
            widget.delete("1.0", "end")
            widget.insert("1.0", value)
        else:
            widget.delete(0, "end")
            widget.insert(0, value)

    add_button.config(text="💾 Update Customer")

def delete_customer(index):
    # Check if user has limited access
    global user_type, limited_user
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to delete customers.\nPlease contact an administrator.")
        return

    if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete this customer?"):
        try:
            customers_data.pop(index)
            save_customers()
            update_customer_preview()
            messagebox.showinfo("Success", "Customer deleted successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete customer: {str(e)}")

# This function has been replaced by a more robust version below

def clear_customer_form():
    global customer_edit_index, add_button, clear_button
    print("=== CLEAR CUSTOMER FORM FUNCTION CALLED ===")
    print(f"Add button state before clearing: {add_button.cget('state')}")

    # Reset edit index
    customer_edit_index = None

    try:
        # Clear all form fields
        for label, widget in customer_entries.items():
            try:
                if isinstance(widget, ExcelText):
                    widget.delete("1.0", "end")
                else:
                    widget.delete(0, "end")
                print(f"Cleared field: {label}")
            except Exception as e:
                print(f"Error clearing field {label}: {str(e)}")
    except Exception as e:
        print(f"Error in clear_customer_form: {str(e)}")

    # Reset button text
    add_button.config(text="➕ Add Customer")

    # Make sure the add button is enabled for all users
    add_button.config(state="normal")
    clear_button.config(state="normal")
    print(f"Add button state after clearing: {add_button.cget('state')}")

    # Force update the UI
    root.update_idletasks()

# Define the add_or_update_customer function first
def add_or_update_customer():
    global customer_edit_index, customers_data, limited_user, add_button, clear_button

    print("=== ADD OR UPDATE CUSTOMER FUNCTION CALLED ===")
    print(f"Limited user: {limited_user}, Edit index: {customer_edit_index}")
    print(f"Add button state before: {add_button.cget('state')}")

    # Force enable the add button just in case it's disabled
    add_button.config(state="normal")

    # Check if user has limited access and is trying to update a customer
    if limited_user and customer_edit_index is not None:
        messagebox.showinfo("Limited Access", "You do not have permission to edit customers.\nPlease contact an administrator.")
        return

    # Limited users can add new customers (when customer_edit_index is None)

    # Validate required fields
    if not customer_entries["Customer Name"].get().strip():
        messagebox.showwarning("Validation Error", "Customer Name is required")
        return

    # Disable the add button to prevent multiple clicks
    add_button.config(state="disabled")
    print(f"Add button state after disabling: {add_button.cget('state')}")

    # Force update the UI to show the disabled button
    root.update_idletasks()

    try:
        # Collect customer data from form
        customer = {}
        for label, widget in customer_entries.items():
            try:
                if isinstance(widget, ExcelText):
                    value = widget.get("1.0", "end").strip()
                else:
                    value = widget.get().strip()
                customer[label] = value
                print(f"Field: {label}, Value: {value}")
            except Exception as field_error:
                print(f"Error getting value for field {label}: {str(field_error)}")
                # Use empty string as fallback
                customer[label] = ""

        # Update existing customer or add new one
        if customer_edit_index is not None:
            print(f"Updating existing customer at index {customer_edit_index}")
            if 0 <= customer_edit_index < len(customers_data):
                customers_data[customer_edit_index] = customer
                customer_edit_index = None
                add_button.config(text="➕ Add Customer")
            else:
                print(f"Invalid customer_edit_index: {customer_edit_index}, adding as new customer")
                customers_data.append(dict(customer))
                customer_edit_index = None
        else:
            print("Adding new customer")
            # Make a deep copy to avoid reference issues
            customers_data.append(dict(customer))
            print(f"Total customers after adding: {len(customers_data)}")

        # Create directory if it doesn't exist
        print(f"Ensuring directory exists: {os.path.dirname(customer_file)}")
        os.makedirs(os.path.dirname(customer_file), exist_ok=True)

        # Save to Excel directly without using save_customers function
        print("Saving customer data to Excel")
        wb = Workbook()
        ws = wb.active
        ws.title = "Customers"

        # Define headers
        headers = ["Sr. No.", "Customer Name", "Email", "Phone Number", "GST Number", "State", "Address", "Shipping To"]

        # Style for headers
        header_style = Font(name='Segoe UI', size=11, bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="2980b9", end_color="2980b9", fill_type="solid")
        header_alignment = Alignment(horizontal='center', vertical='center')

        # Style for data cells
        data_font = Font(name='Segoe UI', size=10)
        data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # Add borders
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            # Set header properties
            cell.value = header
            cell.font = header_style
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border

        # Write data
        for row, customer in enumerate(customers_data, 2):
            # Sr. No.
            ws.cell(row=row, column=1, value=row-1)

            # Customer details
            columns = [
                customer.get("Customer Name", ""),
                customer.get("Email", ""),
                customer.get("Phone Number", ""),
                customer.get("GST Number", ""),
                customer.get("State", ""),
                customer.get("Address", ""),
                customer.get("Shipping To", "")
            ]

            for col, value in enumerate(columns, 2):
                cell = ws.cell(row=row, column=col, value=value)
                cell.font = data_font
                cell.alignment = data_alignment
                cell.border = thin_border

        # Auto-filter
        ws.auto_filter.ref = f"A1:H{len(customers_data) + 1}"

        # Freeze panes
        ws.freeze_panes = 'A2'

        # Save the workbook
        wb.save(customer_file)
        print(f"Successfully saved customer data to {customer_file}")

        # Create backup of the Excel file
        if os.path.exists(customer_file):
            backup_excel_file(customer_file)
            print(f"Created backup of customer file to {BACKUP_DIR}")

        # Update preview immediately
        print("Updating customer preview")
        update_customer_preview()

        # Clear form after successful save
        print("Clearing customer form")
        clear_customer_form()

        # Show success message
        messagebox.showinfo("Success", "Customer saved successfully!")
    except Exception as e:
        print(f"ERROR saving customer: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        print(f"Error traceback: {traceback.format_exc()}")
        messagebox.showerror("Error", f"Error saving customer data: {str(e)}")
    finally:
        # Re-enable the add button
        print("Re-enabling add button in finally block")
        add_button.config(state="normal")
        clear_button.config(state="normal")
        print(f"Add button final state: {add_button.cget('state')}")

        # Force update the UI to show the enabled button
        root.update_idletasks()

# Initialize customers data
customers_data = []
current_edit_index = None

# Customer form layout
customer_form = tk.Frame(customer_layout, bg="white", bd=1, relief="solid")
customer_form.pack(fill="x", pady=(0, 10))

# Add customer search at the top
search_frame = tk.Frame(customer_form, bg="white")
search_frame.pack(fill="x", padx=10, pady=5)

tk.Label(search_frame, text="Search Customer:", font=("Segoe UI", 10, "bold"),
        bg="white").pack(side="left", padx=5)
customer_search = ExcelEntry(search_frame, width=30)
customer_search.pack(side="left", padx=5)
customer_search.bind('<KeyRelease>', lambda e: update_customer_preview())

# Form rows
row1 = tk.Frame(customer_form, bg="white")
row1.pack(fill="x", padx=10, pady=5)

row2 = tk.Frame(customer_form, bg="white")
row2.pack(fill="x", padx=10, pady=5)

row3 = tk.Frame(customer_form, bg="white")
row3.pack(fill="x", padx=10, pady=5)

# Button row
button_row = tk.Frame(customer_form, bg="white")
button_row.pack(fill="x", padx=10, pady=5)

# Create customer_entries dictionary before defining buttons
customer_entries = {}

# First row fields
row1_fields = [
    ("Customer Name", 30),
    ("Email", 30),
    ("Phone Number", 20)
]

# Second row fields
row2_fields = [
    ("GST Number", 20),
    ("State", 20)
]

# Third row fields
row3_fields = [
    ("Address", 50),
    ("Shipping To", 50)
]

customer_entries = {}

# Create fields in first row
for i, (label, width) in enumerate(row1_fields):
    field_frame = tk.Frame(row1, bg="white")
    field_frame.grid(row=0, column=i, padx=5, sticky="ew")

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    entry = ExcelEntry(field_frame, width=width)
    entry.pack(fill="x", pady=2)
    customer_entries[label] = entry

# Create fields in second row
for i, (label, width) in enumerate(row2_fields):
    field_frame = tk.Frame(row2, bg="white")
    field_frame.grid(row=0, column=i, padx=5, sticky="ew")

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    entry = ExcelEntry(field_frame, width=width)
    entry.pack(fill="x", pady=2)
    customer_entries[label] = entry

# Create fields in third row
for i, (label, width) in enumerate(row3_fields):
    field_frame = tk.Frame(row3, bg="white")
    field_frame.grid(row=0, column=i, padx=5, sticky="ew")

    tk.Label(field_frame, text=label, font=("Segoe UI", 10, "bold"),
            bg="white", anchor="w").pack(fill="x", pady=(0, 5))

    entry = ExcelText(field_frame, height=3, width=width)
    entry.pack(fill="x", pady=2)
    customer_entries[label] = entry

# Configure grid columns to be proportional
for row in [row1, row2, row3]:
    for i in range(len(row.winfo_children())):
        row.grid_columnconfigure(i, weight=1)

# Now add the buttons after all form fields are created with modern styling
add_button_frame = create_modern_button(button_row, text="Add Customer",
                                      command=add_or_update_customer,
                                      bg_color=SUCCESS_COLOR, fg_color="white", icon="➕")
add_button_frame.pack(side="right", padx=5, fill="x", expand=True)
add_button = add_button_frame.winfo_children()[0]  # Get the actual button for later reference

clear_button_frame = create_modern_button(button_row, text="Clear Form",
                                        command=clear_customer_form,
                                        bg_color=DANGER_COLOR, fg_color="white", icon="🔄")
clear_button_frame.pack(side="right", padx=5, fill="x", expand=True)
clear_button = clear_button_frame.winfo_children()[0]  # Get the actual button for later reference

# Make sure buttons are enabled
add_button.config(state="normal")
clear_button.config(state="normal")

# Preview section for customers with border
customers_preview_frame = tk.Frame(customer_layout, bg="#f0f0f0", bd=1, relief="solid")
customers_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)

# Create a Treeview for customer preview with action buttons and grid lines
customer_preview = ttk.Treeview(customers_preview_frame,
                              columns=("Sr", "Name", "GST", "Phone", "Email", "Address", "State", "Shipping", "Actions"),
                              show="headings",
                              style="Treeview")
customer_preview.heading("Sr", text="Sr. No.")
customer_preview.heading("Name", text="Customer Name")
customer_preview.heading("GST", text="GST Number")
customer_preview.heading("Phone", text="Phone Number")
customer_preview.heading("Email", text="Email")
customer_preview.heading("Address", text="Address")
customer_preview.heading("State", text="State")
customer_preview.heading("Shipping", text="Shipping To")
customer_preview.heading("Actions", text="Actions")

# Set column widths and alignment for Excel-like appearance - adjusted to fit within window
customer_preview.column("Sr", width=50, anchor="center")
customer_preview.column("Name", width=150)  # Reduced width for customer names
customer_preview.column("GST", width=100, anchor="center")
customer_preview.column("Phone", width=100, anchor="center")
customer_preview.column("Email", width=120)
customer_preview.column("Address", width=150)
customer_preview.column("State", width=80, anchor="center")
customer_preview.column("Shipping", width=150)
customer_preview.column("Actions", width=100, anchor="center")

# Make sure the horizontal scrollbar is enabled
customer_scrollbar_x = ttk.Scrollbar(customers_preview_frame, orient="horizontal", command=customer_preview.xview)
customer_preview.configure(xscrollcommand=customer_scrollbar_x.set)
customer_scrollbar_x.pack(side="bottom", fill="x")

# Configure tags for alternating row colors with more vibrant colors
customer_preview.tag_configure("oddrow", background="#ecf0f1")  # Light blue-gray
customer_preview.tag_configure("evenrow", background="#f5f6fa")  # Very light lavender

# Add scrollbars to treeview
customer_scrollbar = ttk.Scrollbar(customers_preview_frame, orient="vertical", command=customer_preview.yview)
customer_preview.configure(yscrollcommand=customer_scrollbar.set)

# Pack the treeview and scrollbars
customer_preview.pack(side="left", fill="both", expand=True)
customer_scrollbar.pack(side="right", fill="y")

# Bind double-click event to edit customer
customer_preview.bind("<Double-1>", lambda event: edit_selected_customer(event))

def edit_selected_customer(event):
    # Check if user has limited access
    global user_type, limited_user
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to edit customers.\nPlease contact an administrator.")
        return

    # Get selected item
    selected_item = customer_preview.focus()
    if not selected_item:
        return

    # Get the index of the selected item
    selected_index = customer_preview.index(selected_item)
    if selected_index < len(customers_data):
        edit_customer(selected_index)

def update_customer_preview():
    try:
        # Clear existing items
        try:
            for item in customer_preview.get_children():
                customer_preview.delete(item)
        except Exception as clear_error:
            print(f"Error clearing customer preview: {str(clear_error)}")

        # Print debug info
        print(f"Updating customer preview with {len(customers_data)} customers")
        print(f"Customer preview exists: {customer_preview is not None}")
        print(f"Customers preview frame exists: {customers_preview_frame is not None}")

        # Get search text safely
        try:
            search_text = customer_search.get().lower() if customer_search else ""
        except Exception as search_error:
            print(f"Error getting search text: {str(search_error)}")
            search_text = ""

        if not customers_data:
            # If no customers, just return (treeview will be empty)
            print("No customers data to display")
            return

        # Check user permissions - show preview for all users
        global user_type, limited_user

        # Make sure preview is visible for all users
        try:
            customers_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
        except Exception as frame_error:
            print(f"Error showing customers preview frame: {str(frame_error)}")

        # Add all customers to the preview
        filtered_count = 0
        for i, customer in enumerate(customers_data):
            try:
                # Check if customer matches search text
                if search_text and not any(search_text.lower() in str(value).lower() for value in customer.values()):
                    continue

                filtered_count += 1

                # Get address and shipping values, handling both string and text widget formats
                address = customer.get("Address", "")
                if isinstance(address, str):
                    address = address.strip()

                shipping = customer.get("Shipping To", "")
                if isinstance(shipping, str):
                    shipping = shipping.strip()

                # Debug print
                print(f"Adding customer: {customer.get('Customer Name', '')}")

                values = [
                    str(filtered_count),  # Sr. No.
                    customer.get("Customer Name", ""),
                    customer.get("GST Number", ""),
                    customer.get("Phone Number", ""),
                    customer.get("Email", ""),
                    address,
                    customer.get("State", ""),
                    shipping,
                    "🔵 Edit | 🔴 Delete"  # Actions column with colored icons
                ]

                # Add alternating row colors for Excel-like appearance
                tag = "evenrow" if i % 2 == 0 else "oddrow"

                try:
                    customer_preview.insert("", "end", values=values, tags=(tag, f"row_{i}"))
                except Exception as insert_error:
                    print(f"Error inserting customer into preview: {str(insert_error)}")
                    print(f"Customer values: {values}")

            except Exception as customer_error:
                print(f"Error processing customer at index {i}: {str(customer_error)}")
                print(f"Customer data: {customer}")
                continue

        print(f"Added {filtered_count} customers to preview")

        # Force update the UI
        try:
            root.update_idletasks()
        except Exception as update_error:
            print(f"Error updating UI: {str(update_error)}")

    except Exception as e:
        print(f"Error in update_customer_preview: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        print(f"Error traceback: {traceback.format_exc()}")

    # Add right-click menu for edit and delete
    def show_context_menu(event):
        selected_item = customer_preview.identify_row(event.y)
        if selected_item:
            customer_preview.selection_set(selected_item)
            selected_index = customer_preview.index(selected_item)

            # Create colorful context menu
            context_menu = tk.Menu(root, tearoff=0, bg="#f5f6fa", fg="#2c3e50",
                                  activebackground="#4a69bd", activeforeground="white")

            # Check if user has limited access
            global limited_user
            if limited_user:
                context_menu.add_command(label="🔵 Edit (Limited Access)", font=("Segoe UI", 9, "bold"),
                                      command=lambda: messagebox.showinfo("Limited Access",
                                                                        "You do not have permission to edit customers.\nPlease contact an administrator."))
                context_menu.add_command(label="🔴 Delete (Limited Access)", font=("Segoe UI", 9, "bold"),
                                      command=lambda: messagebox.showinfo("Limited Access",
                                                                        "You do not have permission to delete customers.\nPlease contact an administrator."))
            else:
                context_menu.add_command(label="🔵 Edit", font=("Segoe UI", 9, "bold"),
                                      command=lambda: edit_customer(selected_index))
                context_menu.add_command(label="🔴 Delete", font=("Segoe UI", 9, "bold"),
                                      command=lambda: delete_customer(selected_index))

            # Display context menu
            context_menu.post(event.x_root, event.y_root)

    # Bind right-click menu
    customer_preview.bind("<Button-3>", show_context_menu)

    # Bind click on the Actions column
    def on_actions_click(event):
        region = customer_preview.identify_region(event.x, event.y)
        if region == "cell":
            column = customer_preview.identify_column(event.x)
            if customer_preview.column(column, "id") == "Actions":
                item = customer_preview.identify_row(event.y)
                if item:
                    selected_index = customer_preview.index(item)
                    # Show a colorful popup menu
                    actions_menu = tk.Menu(root, tearoff=0, bg="#f5f6fa", fg="#2c3e50",
                                          activebackground="#4a69bd", activeforeground="white")

                    # Check if user has limited access
                    global limited_user
                    if limited_user:
                        actions_menu.add_command(label="🔵 Edit (Limited Access)", font=("Segoe UI", 9, "bold"),
                                              command=lambda: messagebox.showinfo("Limited Access",
                                                                                "You do not have permission to edit customers.\nPlease contact an administrator."))
                        actions_menu.add_command(label="🔴 Delete (Limited Access)", font=("Segoe UI", 9, "bold"),
                                              command=lambda: messagebox.showinfo("Limited Access",
                                                                                "You do not have permission to delete customers.\nPlease contact an administrator."))
                    else:
                        actions_menu.add_command(label="🔵 Edit", font=("Segoe UI", 9, "bold"),
                                              command=lambda: edit_customer(selected_index))
                        actions_menu.add_command(label="🔴 Delete", font=("Segoe UI", 9, "bold"),
                                              command=lambda: delete_customer(selected_index))

                    actions_menu.post(event.x_root, event.y_root)

    # Bind left-click for actions column
    customer_preview.bind("<Button-1>", on_actions_click)

# This function has been moved to the top of the file

# -------- Invoice Tab --------
invoice_frame = frames["Invoice"]
invoice_frame.pack_forget()

# Move these functions before the invoice page creation
def update_totals():
    subtotal = 0
    total_discount = 0
    total_gst = 0

    for item in invoice_items:
        try:
            qty = float(item['quantity'].get() or 0)
            price_per_unit = float(item['price'].get() or 0)
            discount_percent = float(item['discount'].get() or 0)
            gst_percent = float(item['gst'].get() or 0)

            item_subtotal = qty * price_per_unit
            item_discount = item_subtotal * (discount_percent / 100)
            item_amount_after_discount = item_subtotal - item_discount
            item_gst = item_amount_after_discount * (gst_percent / 100)

            subtotal += item_subtotal
            total_discount += item_discount
            total_gst += item_gst
        except ValueError:
            continue

    grand_total = subtotal - total_discount + total_gst
    sgst = total_gst / 2
    cgst = total_gst / 2

    # Update labels with clean formatting
    subtotal_label.config(text=f"Sub Total: {subtotal:.2f}")
    discount_label.config(text=f"Discount: {total_discount:.2f}")
    sgst_label.config(text=f"SGST: {sgst:.2f}")
    cgst_label.config(text=f"CGST: {cgst:.2f}")
    grand_total_label.config(text=f"Grand Total: {grand_total:.2f}")

    # Update amount in words with better formatting
    try:
        amount_int = int(grand_total)
        # Fix decimal part calculation to ensure accuracy
        amount_decimal = round((grand_total - amount_int) * 100)
        amount_words = num2words(amount_int, lang='en_IN').title()

        if amount_decimal > 0:
            decimal_words = num2words(amount_decimal, lang='en_IN').title()
            amount_text = f"{amount_words} Rupees and {decimal_words} Paise Only"
        else:
            amount_text = f"{amount_words} Rupees Only"

        amount_words_value.config(text=amount_text)
    except Exception as e:
        print(f"Error in amount words calculation: {e}")
        amount_words_value.config(text="Zero Rupees Only")

    # Recalculate balance
    calculate_balance()

def calculate_balance():
    try:
        # Extract the grand total value, removing currency symbol
        grand_total_text = grand_total_label.cget("text").split(": ")[1]
        grand_total = float(grand_total_text.replace("₹", "").replace(",", ""))

        # Get received amount from the entry field
        received = float(received_entry.get() or 0)

        # If received amount is zero, pending amount should be zero
        if received == 0:
            balance = 0
        else:
            # Otherwise, subtract received from grand total
            balance = grand_total - received

        # Update received and balance labels
        received_label.config(text=f"Received: ₹{received:.2f}")
        balance_label.config(text=f"Pending Amount: ₹{balance:.2f}")

        # Change balance color based on amount
        if balance <= 0:
            balance_label.config(fg="#2ecc71")  # Green for fully paid
        else:
            balance_label.config(fg="#e74c3c")  # Red for pending
    except (ValueError, IndexError) as e:
        print(f"Error in calculate_balance: {e}")
        received_label.config(text="Received: ₹0.00")
        balance_label.config(text="Pending Amount: ₹0.00")

def determine_invoice_status(received_amount, payment_date_date):
    """
    Determine the status of an invoice based on the received amount and payment date.

    Args:
        received_amount (float): The amount received for the invoice
        payment_date_date (datetime.date): The payment date of the invoice

    Returns:
        str: The status of the invoice (Cleared, Active, or Expired)
    """
    today = datetime.now().date()

    # If pending amount is 0 (fully paid), mark as Cleared
    if received_amount == 0:
        return "Cleared"
    # If payment date has passed and there's still pending amount, mark as Expired
    elif payment_date_date < today and received_amount > 0:
        return "Expired"
    # If payment date is today or in the future and there's pending amount, mark as Active
    else:
        return "Active"

def update_all_invoice_statuses():
    """
    Update the status of all invoices based on the current system date.
    This function reads the Excel file, updates the status of each invoice,
    and saves the file back with color formatting for statuses.
    """
    try:
        # Define the Excel file path
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
        excel_file = os.path.join(invoice_dir, "invoices.xlsx")

        # Check if file exists
        if not os.path.exists(excel_file):
            return  # No file to update

        # Load existing data
        df = pd.read_excel(excel_file)

        if df.empty:
            return  # No data to update

        # Update status for each invoice
        updated = False
        for index, row in df.iterrows():
            received_amount = row["Received Amount"]
            pending_amount = row["Pending Amount"]

            # Convert payment date to datetime.date object
            try:
                payment_date = datetime.strptime(row["Payment Date"], "%d-%m-%Y").date()
            except:
                continue  # Skip if date format is invalid

            # Calculate new status
            new_status = determine_invoice_status(pending_amount, payment_date)

            # Update if status has changed
            if row["Status"] != new_status:
                df.at[index, "Status"] = new_status
                updated = True
                print(f"Updated invoice {row['Invoice No']} status from {row['Status']} to {new_status}")

        # Save updated dataframe if changes were made
        if updated or True:  # Always update to ensure color formatting
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Invoices')

                # Get the worksheet
                worksheet = writer.sheets['Invoices']

                # Format headers
                header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
                header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # Style for data cells
                data_font = Font(name='Segoe UI', size=10)
                data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                number_alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
                center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # Add borders - thicker for better visibility
                medium_border = Side(style='medium')
                thin_border = Side(style='thin')

                # Create different border styles
                all_medium_border = Border(
                    left=medium_border,
                    right=medium_border,
                    top=medium_border,
                    bottom=medium_border
                )

                header_border = Border(
                    left=thin_border,
                    right=thin_border,
                    top=medium_border,
                    bottom=medium_border
                )

                data_border = Border(
                    left=thin_border,
                    right=thin_border,
                    top=thin_border,
                    bottom=thin_border
                )

                # Format headers
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = header_border

                # Apply alternating row colors for better readability
                even_fill = PatternFill(start_color='F9F9F9', end_color='F9F9F9', fill_type='solid')
                odd_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

                # Format data cells
                for row in range(2, len(df) + 2):  # +2 because Excel is 1-indexed and we have a header row
                    # Apply alternating row colors
                    row_fill = even_fill if row % 2 == 0 else odd_fill

                    for col in range(1, len(df.columns) + 1):
                        cell = worksheet.cell(row=row, column=col)
                        cell.font = data_font
                        cell.border = data_border
                        cell.fill = row_fill

                        # Apply specific formatting based on column type
                        col_name = df.columns[col-1]

                        # Numbers and amounts - right aligned
                        if col_name in ["Sr No", "Received Amount", "Pending Amount"]:
                            cell.alignment = number_alignment
                            if col_name in ["Received Amount", "Pending Amount"]:
                                cell.number_format = '#,##0.00'

                        # Dates - center aligned
                        elif col_name in ["Payment Date", "Invoice Date"]:
                            cell.alignment = center_alignment
                            cell.number_format = 'DD-MM-YYYY'

                        # Status - center aligned
                        elif col_name == "Status":
                            cell.alignment = center_alignment

                        # Text fields - left aligned
                        else:
                            cell.alignment = data_alignment

                # Add thick border around the entire table
                max_row = len(df) + 1  # +1 for header
                max_col = len(df.columns)

                # Top border of header row
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=1, column=col)
                    cell.border = Border(
                        left=cell.border.left,
                        right=cell.border.right,
                        top=medium_border,
                        bottom=cell.border.bottom
                    )

                # Bottom border of last row
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=max_row + 1, column=col)
                    cell.border = Border(
                        left=cell.border.left,
                        right=cell.border.right,
                        top=cell.border.top,
                        bottom=medium_border
                    )

                # Left border of first column
                for row in range(1, max_row + 2):
                    cell = worksheet.cell(row=row, column=1)
                    if cell.border:
                        cell.border = Border(
                            left=medium_border,
                            right=cell.border.right,
                            top=cell.border.top,
                            bottom=cell.border.bottom
                        )

                # Right border of last column
                for row in range(1, max_row + 2):
                    cell = worksheet.cell(row=row, column=max_col)
                    if cell.border:
                        cell.border = Border(
                            left=cell.border.left,
                            right=medium_border,
                            top=cell.border.top,
                            bottom=cell.border.bottom
                        )

                # Define status colors
                status_colors = {
                    "Cleared": "0000FF",  # Blue
                    "Active": "00FF00",   # Green
                    "Expired": "FF0000"   # Red
                }

                # Find the status column index
                status_col_idx = None
                for idx, col_name in enumerate(df.columns):
                    if col_name == "Status":
                        status_col_idx = idx + 1  # +1 because Excel is 1-indexed
                        break

                # Apply color formatting to status cells
                if status_col_idx:
                    for row_idx, row in enumerate(df.iterrows(), 2):  # Start from row 2 (after header)
                        status = row[1]["Status"]
                        cell = worksheet.cell(row=row_idx, column=status_col_idx)

                        # Set font color based on status
                        if status in status_colors:
                            cell.font = Font(color=status_colors[status], bold=True)

                # Set column widths
                column_widths = {
                    "Sr No": 6,
                    "Invoice No": 22,
                    "Customer Name": 35,
                    "Mobile": 18,
                    "GST": 22,
                    "Received Amount": 18,
                    "Pending Amount": 18,
                    "Payment Date": 15,
                    "Invoice Date": 15,
                    "Status": 12
                }

                # Apply column widths
                for col, width in column_widths.items():
                    if col in df.columns:
                        col_letter = get_column_letter(df.columns.get_loc(col) + 1)
                        worksheet.column_dimensions[col_letter].width = width

                # Auto-filter for easy sorting and filtering
                worksheet.auto_filter.ref = f"A1:{get_column_letter(len(df.columns))}1"

                # Freeze the header row
                worksheet.freeze_panes = 'A2'

    except Exception as e:
        print(f"Error updating invoice statuses: {str(e)}")

def save_invoice_items_to_excel(invoice_number, customer_name):
    """
    Save the invoice items (products purchased by customer) to a separate Excel file.
    This function is called when an invoice is saved or downloaded.

    Args:
        invoice_number: The invoice number
        customer_name: The name of the customer
    """
    try:
        # Check if there are any invoice items
        if not invoice_items:
            return True

        # Create directory if it doesn't exist
        invoice_items_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Items")
        os.makedirs(invoice_items_dir, exist_ok=True)

        # Define the Excel file path
        excel_file = os.path.join(invoice_items_dir, f"invoice_items.xlsx")

        # Create a new DataFrame for the invoice items
        items_data = []

        # Get current date
        current_date = datetime.now().strftime("%d-%m-%Y")

        # Process each invoice item
        for item in invoice_items:
            try:
                # Get item details
                product_name = item['item_name'].get().strip()
                quantity = item['quantity'].get().strip()
                unit = item['unit'].get().strip()
                price = item['price'].get().strip()
                discount = item['discount'].get().strip()
                gst = item['gst'].get().strip()
                amount = item['amount'].cget("text").replace("₹", "").strip()

                # Skip if product name is empty
                if not product_name:
                    continue

                # Add item to data
                items_data.append({
                    "Invoice No": invoice_number,
                    "Customer Name": customer_name,
                    "Product Name": product_name,
                    "Quantity": quantity,
                    "Unit": unit,
                    "Price": price,
                    "Discount %": discount,
                    "GST %": gst,
                    "Amount": amount,
                    "Invoice Date": current_date
                })
            except Exception as item_error:
                print(f"Error processing invoice item: {str(item_error)}")
                continue

        # If no items were processed, return
        if not items_data:
            return True

        # Check if file exists, if not create it with headers
        if not os.path.exists(excel_file):
            # Create new DataFrame
            df = pd.DataFrame(items_data)

            # Add serial numbers
            df.insert(0, "Sr. No.", range(1, len(df) + 1))
        else:
            # Load existing data
            existing_df = pd.read_excel(excel_file)

            # Create new DataFrame with the new items
            new_df = pd.DataFrame(items_data)

            # Add serial numbers starting from the last one in the existing DataFrame
            start_sr_no = 1
            if not existing_df.empty and "Sr. No." in existing_df.columns:
                start_sr_no = existing_df["Sr. No."].max() + 1

            new_df.insert(0, "Sr. No.", range(start_sr_no, start_sr_no + len(new_df)))

            # Concatenate the existing and new DataFrames
            df = pd.concat([existing_df, new_df], ignore_index=True)

        # Save to Excel with proper formatting
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Invoice Items')

            # Get the worksheet
            worksheet = writer.sheets['Invoice Items']

            # Format headers
            header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Apply alternating row colors for better readability
            even_fill = PatternFill(start_color='F9F9F9', end_color='F9F9F9', fill_type='solid')
            odd_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

            # Format data cells
            for row in range(2, len(df) + 2):  # +2 because Excel is 1-indexed and we have a header row
                # Apply alternating row colors
                row_fill = even_fill if row % 2 == 0 else odd_fill

                for col in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.font = Font(name='Segoe UI', size=10)
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                    cell.fill = row_fill

            # Set column widths
            column_widths = {
                "Sr. No.": 8,
                "Invoice No": 15,
                "Customer Name": 30,
                "Product Name": 30,
                "Quantity": 12,
                "Unit": 10,
                "Price": 12,
                "Discount %": 12,
                "GST %": 10,
                "Amount": 15,
                "Invoice Date": 15
            }

            # Apply column widths
            for col, width in column_widths.items():
                if col in df.columns:
                    col_letter = get_column_letter(df.columns.get_loc(col) + 1)
                    worksheet.column_dimensions[col_letter].width = width

            # Auto-filter for easy sorting and filtering
            worksheet.auto_filter.ref = f"A1:{get_column_letter(len(df.columns))}1"

            # Freeze the header row
            worksheet.freeze_panes = 'A2'

        # Create backup of the Excel file
        if os.path.exists(excel_file):
            backup_excel_file(excel_file)

        print(f"Successfully saved invoice items to {excel_file}")
        return True
    except Exception as e:
        print(f"Error saving invoice items: {str(e)}")
        return False

def update_product_quantities():
    """
    Update product quantities by subtracting the quantities used in the invoice.
    This function is called when an invoice is saved or downloaded.
    """
    try:
        # Check if there are any invoice items
        if not invoice_items:
            return True

        # Create a copy of products_data to work with
        global products_data
        updated_products = products_data.copy()

        # Track which products were updated
        updated_product_names = []

        # Process each invoice item
        for item in invoice_items:
            try:
                # Get product name and quantity from invoice item
                product_name = item['item_name'].get().strip()
                quantity_str = item['quantity'].get().strip()

                # Skip if product name or quantity is empty
                if not product_name or not quantity_str:
                    continue

                # Convert quantity to float
                try:
                    invoice_quantity = float(quantity_str)
                except ValueError:
                    print(f"Invalid quantity for product {product_name}: {quantity_str}")
                    continue

                # Find the product in products_data
                product_found = False
                for i, product in enumerate(updated_products):
                    if product.get("Product Name", "").strip() == product_name:
                        # Found the product, update its quantity
                        try:
                            current_quantity = float(product.get("Quantity", 0))
                            new_quantity = max(0, current_quantity - invoice_quantity)
                            updated_products[i]["Quantity"] = str(new_quantity)
                            updated_product_names.append(product_name)
                            product_found = True
                            break
                        except ValueError:
                            print(f"Invalid current quantity for product {product_name}: {product.get('Quantity', 0)}")
                            continue

                if not product_found:
                    print(f"Product not found in inventory: {product_name}")
            except Exception as item_error:
                print(f"Error processing invoice item: {str(item_error)}")
                continue

        # If no products were updated, return
        if not updated_product_names:
            return True

        # Update products_data with the updated quantities
        products_data = updated_products

        # Save the updated products to Excel
        if products_data:
            try:
                # Create a new DataFrame with properly organized columns
                df = pd.DataFrame()

                # Add serial numbers
                df['Sr. No.'] = range(1, len(products_data) + 1)

                # Add product details in correct order
                columns = ['Product Name', 'HSN Code', 'Unit', 'Quantity', 'Rate', 'Tax %', 'Low Stock Alert', 'Manufacture Date', 'Expiry Date']
                for col in columns:
                    df[col] = [product.get(col, '') for product in products_data]

                # Save to Excel with proper formatting
                with pd.ExcelWriter(products_file, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Products')

                    # Get the worksheet
                    worksheet = writer.sheets['Products']

                    # Format headers
                    header_font = Font(bold=True)
                    header_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                    header_alignment = Alignment(horizontal='center')

                    for cell in worksheet[1]:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment

                    # Set column widths
                    column_widths = {
                        'Sr. No.': 8,
                        'Product Name': 30,
                        'HSN Code': 15,
                        'Unit': 10,
                        'Quantity': 12,
                        'Rate': 12,
                        'Tax %': 10,
                        'Low Stock Alert': 15,
                        'Manufacture Date': 15,
                        'Expiry Date': 15
                    }

                    for col, width in column_widths.items():
                        worksheet.column_dimensions[get_column_letter(df.columns.get_loc(col) + 1)].width = width

                # Create backup of the Excel file
                if os.path.exists(products_file):
                    backup_excel_file(products_file)

                # Update the product preview if it exists
                if 'update_product_preview' in globals():
                    update_product_preview()

                print(f"Updated quantities for products: {', '.join(updated_product_names)}")
                return True
            except Exception as save_error:
                print(f"Error saving updated products: {str(save_error)}")
                return False

        return True
    except Exception as e:
        print(f"Error updating product quantities: {str(e)}")
        return False

def save_invoice_to_excel():
    try:
        # Get customer details
        customer_name = customer_search_var.get()

        # Initialize customer variable
        customer = None

        # If customer is selected, find it in customers_data
        if customer_name and customer_name != "Select Customer":
            customer = next((c for c in customers_data if c["Customer Name"] == customer_name), None)

        # Get invoice details
        try:
            # Remove currency symbol and convert to float
            grand_total_text = grand_total_label.cget("text").split(": ")[1]
            grand_total = float(grand_total_text.replace("₹", "").replace(",", ""))
            # Get received amount
            received_amount = float(received_entry.get() or 0)

            # Calculate pending amount based on the same logic
            if received_amount == 0:
                balance = 0
            else:
                # Get grand total for calculation
                grand_total_text = grand_total_label.cget("text").split(": ")[1]
                grand_total_value = float(grand_total_text.replace("₹", "").replace(",", ""))
                balance = grand_total_value - received_amount
            # Get the payment date from the DateEntry widget
            payment_date_str = payment_date.get()

            # Validate payment date
            try:
                # The DateEntry widget uses dd-mm-yyyy format
                payment_date_obj = datetime.strptime(payment_date_str, "%d-%m-%Y")
            except ValueError:
                messagebox.showerror("Error", "Invalid payment date format")
                return

            # Determine status based on the specified logic
            payment_date_date = payment_date_obj.date()
            status = determine_invoice_status(received_amount, payment_date_date)

        except (ValueError, IndexError) as e:
            messagebox.showerror("Error", f"Error processing invoice values: {str(e)}")
            return

        # Create directory if it doesn't exist
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
        os.makedirs(invoice_dir, exist_ok=True)

        # Define the Excel file path
        excel_file = os.path.join(invoice_dir, "invoices.xlsx")

        # Check if file exists, if not create it with headers
        if not os.path.exists(excel_file):
            # Create new workbook with headers
            wb = Workbook()
            ws = wb.active
            ws.title = "Invoices"

            # Define headers
            headers = ["Sr No", "Invoice No", "Customer Name", "Mobile", "GST", "Received Amount", "Pending Amount", "Payment Date", "Invoice Date", "Status"]

            # Add headers with styling
            header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # Style for data cells
            data_font = Font(name='Segoe UI', size=10)
            data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            number_alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # Add borders - thicker for better visibility
            medium_border = Side(style='medium')
            thin_border = Side(style='thin')

            # Create different border styles
            header_border = Border(
                left=thin_border,
                right=thin_border,
                top=medium_border,
                bottom=medium_border
            )

            data_border = Border(
                left=thin_border,
                right=thin_border,
                top=thin_border,
                bottom=thin_border
            )

            # Format headers
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = header_border

            # Set number formats for amount columns
            for col, header in enumerate(headers, 1):
                if header in ["Received Amount", "Pending Amount"]:
                    col_letter = get_column_letter(col)
                    ws.column_dimensions[col_letter].number_format = '#,##0.00'
                elif header in ["Payment Date", "Invoice Date"]:
                    col_letter = get_column_letter(col)
                    ws.column_dimensions[col_letter].number_format = 'DD-MM-YYYY'

            # Add thick border around the table
            # Top border of header row already set

            # Left border of first column
            for row in range(1, 2):  # Just header row for now
                cell = ws.cell(row=row, column=1)
                cell.border = Border(
                    left=medium_border,
                    right=cell.border.right,
                    top=cell.border.top,
                    bottom=cell.border.bottom
                )

            # Right border of last column
            for row in range(1, 2):  # Just header row for now
                cell = ws.cell(row=row, column=len(headers))
                cell.border = Border(
                    left=cell.border.left,
                    right=medium_border,
                    top=cell.border.top,
                    bottom=cell.border.bottom
                )

            # Set column widths
            column_widths = {
                "Sr No": 6,
                "Invoice No": 22,
                "Customer Name": 35,
                "Mobile": 18,
                "GST": 22,
                "Received Amount": 18,
                "Pending Amount": 18,
                "Payment Date": 15,
                "Invoice Date": 15,
                "Status": 12
            }

            # Apply column widths
            for col_idx, header in enumerate(headers, 1):
                col_letter = get_column_letter(col_idx)
                if header in column_widths:
                    ws.column_dimensions[col_letter].width = column_widths[header]

            # Auto-filter for easy sorting and filtering
            ws.auto_filter.ref = f"A1:{get_column_letter(len(headers))}1"

            # Freeze the header row
            ws.freeze_panes = 'A2'

            # Save the new workbook
            wb.save(excel_file)

        # Now read the existing file to append data
        try:
            # Load existing data
            df = pd.read_excel(excel_file)

            # Get current invoice date
            invoice_date = datetime.now().strftime("%d-%m-%Y")

            # Create new row data
            new_row = {
                "Sr No": len(df) + 1,
                "Invoice No": invoice_number,
                "Customer Name": customer_name if customer_name and customer_name != "Select Customer" else "",
                "Mobile": customer.get("Phone Number", "") if customer else "",
                "GST": customer.get("GST Number", "") if customer else "",
                "Received Amount": received_amount,
                "Pending Amount": balance,
                "Payment Date": payment_date_date.strftime("%d-%m-%Y"),
                "Invoice Date": invoice_date,
                "Status": status
            }

            # Append new row to dataframe
            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

            # Save updated dataframe to Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Invoices')

                # Get the worksheet
                worksheet = writer.sheets['Invoices']

                # Format headers
                header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
                header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # Style for data cells
                data_font = Font(name='Segoe UI', size=10)
                data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                number_alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
                center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # Add borders - thicker for better visibility
                medium_border = Side(style='medium')
                thin_border = Side(style='thin')

                # Create different border styles
                all_medium_border = Border(
                    left=medium_border,
                    right=medium_border,
                    top=medium_border,
                    bottom=medium_border
                )

                header_border = Border(
                    left=thin_border,
                    right=thin_border,
                    top=medium_border,
                    bottom=medium_border
                )

                data_border = Border(
                    left=thin_border,
                    right=thin_border,
                    top=thin_border,
                    bottom=thin_border
                )

                # Format headers
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = header_border

                # Apply alternating row colors for better readability
                even_fill = PatternFill(start_color='F9F9F9', end_color='F9F9F9', fill_type='solid')
                odd_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

                # Format data cells
                for row in range(2, len(df) + 2):  # +2 because Excel is 1-indexed and we have a header row
                    # Apply alternating row colors
                    row_fill = even_fill if row % 2 == 0 else odd_fill

                    for col in range(1, len(df.columns) + 1):
                        cell = worksheet.cell(row=row, column=col)
                        cell.font = data_font
                        cell.border = data_border
                        cell.fill = row_fill

                        # Apply specific formatting based on column type
                        col_name = df.columns[col-1]

                        # Numbers and amounts - right aligned
                        if col_name in ["Sr No", "Received Amount", "Pending Amount"]:
                            cell.alignment = number_alignment
                            if col_name in ["Received Amount", "Pending Amount"]:
                                cell.number_format = '#,##0.00'

                        # Dates - center aligned
                        elif col_name in ["Payment Date", "Invoice Date"]:
                            cell.alignment = center_alignment
                            cell.number_format = 'DD-MM-YYYY'

                        # Status - center aligned
                        elif col_name == "Status":
                            cell.alignment = center_alignment

                        # Text fields - left aligned
                        else:
                            cell.alignment = data_alignment

                # Add thick border around the entire table
                max_row = len(df) + 1  # +1 for header
                max_col = len(df.columns)

                # Top border of header row
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=1, column=col)
                    cell.border = Border(
                        left=cell.border.left,
                        right=cell.border.right,
                        top=medium_border,
                        bottom=cell.border.bottom
                    )

                # Bottom border of last row
                for col in range(1, max_col + 1):
                    cell = worksheet.cell(row=max_row + 1, column=col)
                    cell.border = Border(
                        left=cell.border.left,
                        right=cell.border.right,
                        top=cell.border.top,
                        bottom=medium_border
                    )

                # Left border of first column
                for row in range(1, max_row + 2):
                    cell = worksheet.cell(row=row, column=1)
                    if cell.border:
                        cell.border = Border(
                            left=medium_border,
                            right=cell.border.right,
                            top=cell.border.top,
                            bottom=cell.border.bottom
                        )

                # Right border of last column
                for row in range(1, max_row + 2):
                    cell = worksheet.cell(row=row, column=max_col)
                    if cell.border:
                        cell.border = Border(
                            left=cell.border.left,
                            right=medium_border,
                            top=cell.border.top,
                            bottom=cell.border.bottom
                        )

                # Define status colors
                status_colors = {
                    "Cleared": "0000FF",  # Blue
                    "Active": "00FF00",   # Green
                    "Expired": "FF0000"   # Red
                }

                # Find the status column index
                status_col_idx = None
                for idx, col_name in enumerate(df.columns):
                    if col_name == "Status":
                        status_col_idx = idx + 1  # +1 because Excel is 1-indexed
                        break

                # Apply color formatting to status cells
                if status_col_idx:
                    for row_idx, row in enumerate(df.iterrows(), 2):  # Start from row 2 (after header)
                        status = row[1]["Status"]
                        cell = worksheet.cell(row=row_idx, column=status_col_idx)

                        # Set font color based on status
                        if status in status_colors:
                            cell.font = Font(color=status_colors[status], bold=True)

                # Set column widths
                column_widths = {
                    "Sr No": 6,
                    "Invoice No": 22,
                    "Customer Name": 35,
                    "Mobile": 18,
                    "GST": 22,
                    "Received Amount": 18,
                    "Pending Amount": 18,
                    "Payment Date": 15,
                    "Invoice Date": 15,
                    "Status": 12
                }

                # Apply column widths
                for col, width in column_widths.items():
                    if col in df.columns:
                        col_letter = get_column_letter(df.columns.get_loc(col) + 1)
                        worksheet.column_dimensions[col_letter].width = width

                # Auto-filter for easy sorting and filtering
                worksheet.auto_filter.ref = f"A1:{get_column_letter(len(df.columns))}1"

                # Freeze the header row
                worksheet.freeze_panes = 'A2'

            # Create backup of the Excel file
            if os.path.exists(excel_file):
                backup_excel_file(excel_file)

            # Save invoice items to Excel
            invoice_items_saved = save_invoice_items_to_excel(invoice_number, customer_name if customer_name and customer_name != "Select Customer" else "")

            # Ask user if they want to update product quantities
            if messagebox.askyesno("Update Product Quantities",
                                 "Do you want to update product quantities in inventory?\n\n"
                                 "This will subtract the quantities used in this invoice from the product inventory."):
                # Update product quantities
                if update_product_quantities():
                    if invoice_items_saved:
                        messagebox.showinfo("Success",
                                          f"Invoice saved successfully to:\n{excel_file}\n\n"
                                          f"Invoice items have been saved.\n\n"
                                          f"Product quantities have been updated in inventory.")
                    else:
                        messagebox.showinfo("Partial Success",
                                          f"Invoice saved successfully to:\n{excel_file}\n\n"
                                          f"Product quantities have been updated in inventory.\n\n"
                                          f"However, there was an error saving invoice items.")
                else:
                    if invoice_items_saved:
                        messagebox.showinfo("Partial Success",
                                          f"Invoice saved successfully to:\n{excel_file}\n\n"
                                          f"Invoice items have been saved.\n\n"
                                          f"However, there was an error updating product quantities.")
                    else:
                        messagebox.showinfo("Partial Success",
                                          f"Invoice saved successfully to:\n{excel_file}\n\n"
                                          f"However, there were errors saving invoice items and updating product quantities.")
            else:
                if invoice_items_saved:
                    messagebox.showinfo("Success",
                                      f"Invoice saved successfully to:\n{excel_file}\n\n"
                                      f"Invoice items have been saved.")
                else:
                    messagebox.showinfo("Partial Success",
                                      f"Invoice saved successfully to:\n{excel_file}\n\n"
                                      f"However, there was an error saving invoice items.")

        except Exception as e:
            messagebox.showerror("Error", f"Error saving invoice data: {str(e)}")

    except Exception as e:
        messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")

def preview_invoice():
    # Create preview window
    preview_window = tk.Toplevel(root)
    preview_window.title("Invoice Preview")
    preview_window.state('zoomed')

    # Create main canvas with scrollbar
    preview_canvas = tk.Canvas(preview_window, bg="#f0f0f0")
    preview_scrollbar = tk.Scrollbar(preview_window, orient="vertical", command=preview_canvas.yview)

    # Create A4 sized frame (A4 dimensions: 210mm x 297mm, converted to pixels at 96 DPI)
    preview_frame = tk.Frame(preview_canvas, bg="white", width=794, height=1123)
    preview_frame.pack(padx=20, pady=20)
    preview_frame.pack_propagate(False)

    # Configure canvas
    preview_canvas.create_window((0, 0), window=preview_frame, anchor="nw")
    preview_canvas.configure(yscrollcommand=preview_scrollbar.set)

    # Pack canvas and scrollbar
    preview_canvas.pack(side="left", fill="both", expand=True)
    preview_scrollbar.pack(side="right", fill="y")

    # Upper Header with TAX INVOICE and Invoice Details
    upper_header = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    upper_header.pack(fill="x", padx=40, pady=(20, 10))

    # TAX INVOICE title
    title_frame = tk.Frame(upper_header, bg="white")
    title_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(title_frame, text="INVOICE", font=("Segoe UI", 16, "bold"),
            bg="white").pack(side="left")

    # Invoice number and date
    details_frame = tk.Frame(upper_header, bg="white")
    details_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(details_frame, text=f"Invoice No: {invoice_number}",
            font=("Segoe UI", 10, "bold"), bg="white").pack(side="right")
    tk.Label(details_frame, text=f"Date: {invoice_date}",
            font=("Segoe UI", 10), bg="white").pack(side="right", padx=20)

    # Company Details Section
    company_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    company_frame.pack(fill="x", padx=40, pady=(0, 10))

    company_header = tk.Frame(company_frame, bg="#2f3640", height=25)
    company_header.pack(fill="x")
    tk.Label(company_header, text="Company Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    company_content = tk.Frame(company_frame, bg="white")
    company_content.pack(fill="x", padx=10, pady=5)

    # Display company logo
    if "Company Logo" in preview_data and os.path.exists(preview_data["Company Logo"]):
        try:
            img = Image.open(preview_data["Company Logo"])
            img = img.resize((80, 80))
            logo = ImageTk.PhotoImage(img)
            logo_label = tk.Label(company_content, image=logo, bg="white")
            logo_label.image = logo
            logo_label.pack(side="left", padx=10)
        except Exception as e:
            print(f"Error loading company logo: {e}")

    # Company details in two columns
    company_left = tk.Frame(company_content, bg="white")
    company_left.pack(side="left", fill="x", expand=True, padx=5)

    company_right = tk.Frame(company_content, bg="white")
    company_right.pack(side="left", fill="x", expand=True, padx=5)

    # Display company details
    for label, value in company_fields_left:
        frame = tk.Frame(company_left, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        tk.Label(frame, text=preview_data.get(label, ""), font=("Segoe UI", 9),
                bg="white").pack(side="left", padx=5)

    for label, value in company_fields_right:
        frame = tk.Frame(company_right, bg="white")
        frame.pack(fill="x", pady=2)
        tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                bg="white", width=15).pack(side="left")
        tk.Label(frame, text=preview_data.get(label, ""), font=("Segoe UI", 9),
                bg="white").pack(side="left", padx=5)

    # Customer Details Section
    customer_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    customer_frame.pack(fill="x", padx=40, pady=(0, 10))

    customer_header = tk.Frame(customer_frame, bg="#2f3640", height=25)
    customer_header.pack(fill="x")
    tk.Label(customer_header, text="Customer Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    customer_content = tk.Frame(customer_frame, bg="white")
    customer_content.pack(fill="x", padx=10, pady=5)

    # Get customer details
    customer_name = customer_search_var.get()
    if customer_name and customer_name != "Select Customer":
        customer = next((c for c in customers_data if c["Customer Name"] == customer_name), None)
        if customer:
            customer_data = [
                ("Customer Name", customer.get("Customer Name", "")),
                ("Email", customer.get("Email", "")),
                ("Phone Number", customer.get("Phone Number", "")),
                ("GST Number", customer.get("GST Number", "")),
                ("State", customer.get("State", "")),
                ("Address", customer.get("Address", "")),
                ("Shipping To", customer.get("Shipping To", ""))
            ]
        else:
            customer_data = [
                ("Customer Name", customer_entries["Customer Name"].get()),
                ("Email", customer_entries["Email"].get()),
                ("Phone Number", customer_entries["Phone Number"].get()),
                ("GST Number", customer_entries["GST Number"].get()),
                ("State", customer_entries["State"].get()),
                ("Address", customer_entries["Address"].get("1.0", "end-1c")),
                ("Shipping To", customer_entries["Shipping To"].get("1.0", "end-1c"))
            ]

        # Display customer details in two columns
        customer_left = tk.Frame(customer_content, bg="white")
        customer_left.pack(side="left", fill="x", expand=True, padx=5)

        customer_right = tk.Frame(customer_content, bg="white")
        customer_right.pack(side="left", fill="x", expand=True, padx=5)

        # Left column fields (first 5 fields)
        for i, (label, value) in enumerate(customer_data[:5]):
            frame = tk.Frame(customer_left, bg="white")
            frame.pack(fill="x", pady=2)
            tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                    bg="white", width=15).pack(side="left")
            tk.Label(frame, text=value, font=("Segoe UI", 9),
                    bg="white").pack(side="left", padx=5)

        # Right column fields (remaining fields - Address and Shipping To)
        for i, (label, value) in enumerate(customer_data[5:]):
            frame = tk.Frame(customer_right, bg="white")
            frame.pack(fill="x", pady=2)
            tk.Label(frame, text=f"{label}:", font=("Segoe UI", 9, "bold"),
                    bg="white", width=15).pack(side="left")
            tk.Label(frame, text=value, font=("Segoe UI", 9),
                    bg="white").pack(side="left", padx=5)

    # Products Table
    products_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    products_frame.pack(fill="x", padx=40, pady=(0, 10))

    # Table headers
    headers = ["Sr.", "Item Name", "Quantity", "Unit", "Price/Unit", "Discount", "GST%", "Amount"]
    header_frame = tk.Frame(products_frame, bg="#2f3640")
    header_frame.pack(fill="x")

    for i, header in enumerate(headers):
        width = 8 if header in ["Sr.", "GST%"] else 15 if header in ["Quantity", "Unit", "Discount"] else 20
        tk.Label(header_frame, text=header, font=("Segoe UI", 9, "bold"),
                bg="#2f3640", fg="white", width=width).grid(row=0, column=i, padx=1, pady=2)

    # Table content
    content_frame = tk.Frame(products_frame, bg="white")
    content_frame.pack(fill="x", padx=5, pady=5)

    for i, item in enumerate(invoice_items):
        row_frame = tk.Frame(content_frame, bg="white")
        row_frame.pack(fill="x", pady=1)

        # Serial number
        tk.Label(row_frame, text=str(i+1), font=("Segoe UI", 9),
                bg="white", width=8).grid(row=0, column=0, padx=1)

        # Item details
        tk.Label(row_frame, text=item['item_name'].get(), font=("Segoe UI", 9),
                bg="white", width=20).grid(row=0, column=1, padx=1)
        tk.Label(row_frame, text=item['quantity'].get(), font=("Segoe UI", 9),
                bg="white", width=15).grid(row=0, column=2, padx=1)
        tk.Label(row_frame, text=item['unit'].get(), font=("Segoe UI", 9),
                bg="white", width=15).grid(row=0, column=3, padx=1)
        tk.Label(row_frame, text=item['price'].get(), font=("Segoe UI", 9),
                bg="white", width=15).grid(row=0, column=4, padx=1)
        tk.Label(row_frame, text=item['discount'].get(), font=("Segoe UI", 9),
                bg="white", width=15).grid(row=0, column=5, padx=1)
        tk.Label(row_frame, text=item['gst'].get(), font=("Segoe UI", 9),
                bg="white", width=8).grid(row=0, column=6, padx=1)
        tk.Label(row_frame, text=item['amount'].cget("text"), font=("Segoe UI", 9),
                bg="white", width=20).grid(row=0, column=7, padx=1)

    # Totals Section
    totals_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    totals_frame.pack(fill="x", padx=40, pady=(0, 10))

    # Amount in words
    amount_words_frame = tk.Frame(totals_frame, bg="white")
    amount_words_frame.pack(fill="x", padx=10, pady=5)
    tk.Label(amount_words_frame, text="Amount in Words:", font=("Segoe UI", 9, "bold"),
            bg="white").pack(side="left")
    tk.Label(amount_words_frame, text=amount_words_value.cget("text"),
            font=("Segoe UI", 9), bg="white").pack(side="left", padx=5)

    # Calculations
    calculations_frame = tk.Frame(totals_frame, bg="white")
    calculations_frame.pack(fill="x", padx=10, pady=5)

    # Left side - empty for spacing
    tk.Frame(calculations_frame, width=400, bg="white").pack(side="left")

    # Right side - calculations
    calc_right = tk.Frame(calculations_frame, bg="white")
    calc_right.pack(side="right")

    for label in [subtotal_label, discount_label, sgst_label, cgst_label,
                 grand_total_label, received_label, balance_label]:
        tk.Label(calc_right, text=label.cget("text"),
                font=label.cget("font"), bg="white").pack(anchor="e")

    # Bank Details
    bank_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    bank_frame.pack(fill="x", padx=40, pady=(0, 10))

    bank_header = tk.Frame(bank_frame, bg="#2f3640")
    bank_header.pack(fill="x")
    tk.Label(bank_header, text="Bank Details", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    bank_content = tk.Frame(bank_frame, bg="white")
    bank_content.pack(fill="x", padx=10, pady=5)

    for detail in bank_details:
        tk.Label(bank_content, text=detail, font=("Segoe UI", 9),
                bg="white").pack(anchor="w")

    # Company Seal and Signature
    sign_frame = tk.Frame(preview_frame, bg="white", bd=1, relief="solid")
    sign_frame.pack(fill="x", padx=40, pady=(0, 10))

    sign_header = tk.Frame(sign_frame, bg="#2f3640", height=25)
    sign_header.pack(fill="x")
    tk.Label(sign_header, text="Company Seal and Sign", font=("Segoe UI", 9, "bold"),
            bg="#2f3640", fg="white").pack(padx=5, pady=2)

    sign_content = tk.Frame(sign_frame, bg="white")
    sign_content.pack(fill="x", padx=10, pady=5)

    # Create right-aligned frame for signature
    signature = tk.Frame(sign_content, bg="white")
    signature.pack(side="right", padx=20, pady=5)

    tk.Label(signature, text="For", font=("Segoe UI", 9),
            bg="white").pack(anchor="e")
    tk.Label(signature, text=preview_data.get("Company Name", ""),
            font=("Segoe UI", 10, "bold"), bg="white").pack(anchor="e")

    if "Stamp/Sign" in preview_data and os.path.exists(preview_data["Stamp/Sign"]):
        try:
            img = Image.open(preview_data["Stamp/Sign"])
            img = img.resize((120, 120))
            seal = ImageTk.PhotoImage(img)
            seal_label = tk.Label(signature, image=seal, bg="white")
            seal_label.image = seal
            seal_label.pack(pady=(10, 5))
        except Exception as e:
            print(f"Error loading stamp/sign: {e}")

    tk.Label(signature, text="Company Seal and Sign",
            font=("Segoe UI", 9), bg="white").pack(anchor="e", pady=(5, 0))

# -------- Invoice Tracking Tab --------
invoice_tracking_frame = frames["Invoice Tracking"]
invoice_tracking_frame.pack_forget()

def create_invoice_tracking_content():
    # Clear any existing widgets
    for widget in invoice_tracking_frame.winfo_children():
        widget.destroy()

    # Create main layout
    tracking_layout = tk.Frame(invoice_tracking_frame, bg="white")
    tracking_layout.pack(fill="both", expand=True, padx=15, pady=10)

    # Title
    tk.Label(tracking_layout, text="📊 Active & Expired Invoice Tracking", font=("Segoe UI", 15, "bold"), bg="white", anchor="w").pack(fill="x", pady=(0, 10))

    # Check user permissions
    global user_type
    limited_user = (user_type == "limited")

    # Create a frame for the invoice table
    table_frame = tk.Frame(tracking_layout, bg="white", bd=1, relief="solid")
    table_frame.pack(fill="both", expand=True, pady=10)

    # Create a Treeview for invoices
    columns = ("Sr", "Invoice", "Copy", "Customer", "Mobile", "Received", "Pending", "PayDate", "InvDate", "Status", "Actions")
    invoice_tree = ttk.Treeview(table_frame, columns=columns, show="headings", style="Treeview")

    # Define columns
    invoice_tree.heading("Sr", text="Sr.")
    invoice_tree.heading("Invoice", text="Invoice No")
    invoice_tree.heading("Copy", text="Copy")
    invoice_tree.heading("Customer", text="Customer Name")
    invoice_tree.heading("Mobile", text="Mobile")
    invoice_tree.heading("Received", text="Received Amount")
    invoice_tree.heading("Pending", text="Pending Amount")
    invoice_tree.heading("PayDate", text="Payment Date")
    invoice_tree.heading("InvDate", text="Invoice Date")
    invoice_tree.heading("Status", text="Status")
    invoice_tree.heading("Actions", text="Actions")

    # Set column widths
    invoice_tree.column("Sr", width=40, anchor="center")
    invoice_tree.column("Invoice", width=120, anchor="center")
    invoice_tree.column("Copy", width=50, anchor="center")
    invoice_tree.column("Customer", width=150)
    invoice_tree.column("Mobile", width=100, anchor="center")
    invoice_tree.column("Received", width=100, anchor="center")
    invoice_tree.column("Pending", width=100, anchor="center")
    invoice_tree.column("PayDate", width=100, anchor="center")
    invoice_tree.column("InvDate", width=100, anchor="center")
    invoice_tree.column("Status", width=80, anchor="center")
    invoice_tree.column("Actions", width=80, anchor="center")

    # Configure tags for status colors
    invoice_tree.tag_configure("cleared", foreground="#0000FF", font=("Segoe UI", 9, "bold"))  # Blue
    invoice_tree.tag_configure("active", foreground="#00FF00", font=("Segoe UI", 9, "bold"))   # Green
    invoice_tree.tag_configure("expired", foreground="#FF0000", font=("Segoe UI", 9, "bold"))  # Red

    # Add scrollbars
    y_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=invoice_tree.yview)
    invoice_tree.configure(yscrollcommand=y_scrollbar.set)

    x_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=invoice_tree.xview)
    invoice_tree.configure(xscrollcommand=x_scrollbar.set)

    # Pack the treeview and scrollbars
    invoice_tree.pack(side="left", fill="both", expand=True)
    y_scrollbar.pack(side="right", fill="y")
    x_scrollbar.pack(side="bottom", fill="x")

    # Load invoice data
    invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
    excel_file = os.path.join(invoice_dir, "invoices.xlsx")

    if os.path.exists(excel_file):
        try:
            # Load invoice data
            df = pd.read_excel(excel_file)

            if not df.empty:
                # Filter out cleared invoices - only show Active and Expired
                active_expired_df = df[df["Status"].isin(["Active", "Expired"])]

                if active_expired_df.empty:
                    # Show message if no active or expired invoices
                    tk.Label(table_frame, text="No active or expired invoices found", font=("Segoe UI", 12), bg="white").pack(pady=20)
                else:
                    # Add invoices to the treeview
                    for i, (_, row) in enumerate(active_expired_df.iterrows(), 1):
                        # Determine tag based on status
                        status = row["Status"]
                        tag = status.lower()

                        values = [
                            str(i),
                            row["Invoice No"],
                            "📋",
                            row["Customer Name"],
                            row["Mobile"],
                            f"₹{row['Received Amount']:.2f}",
                            f"₹{row['Pending Amount']:.2f}",
                            row["Payment Date"],
                            row["Invoice Date"],
                            row["Status"],
                            "💰 Pay"
                        ]
                        invoice_tree.insert("", "end", values=values, tags=(tag,))
            else:
                # Show message if no invoices
                tk.Label(table_frame, text="No invoices found", font=("Segoe UI", 12), bg="white").pack(pady=20)
        except Exception as e:
            # Show error message
            tk.Label(table_frame, text=f"Error loading invoices: {str(e)}", font=("Segoe UI", 12), bg="white", fg="red").pack(pady=20)
    else:
        # Show message if file doesn't exist
        tk.Label(table_frame, text="Invoice file not found", font=("Segoe UI", 12), bg="white").pack(pady=20)

    # Function to copy invoice number to clipboard
    def copy_invoice_number(invoice_no):
        root.clipboard_clear()
        root.clipboard_append(invoice_no)
        messagebox.showinfo("Copied", f"Invoice number {invoice_no} copied to clipboard")

    # Function to handle clicks on the tree
    def on_actions_click(event):
        region = invoice_tree.identify_region(event.x, event.y)
        if region == "cell":
            column = invoice_tree.identify_column(event.x)
            item = invoice_tree.identify_row(event.y)
            if item:
                # Get the invoice number from the selected item
                invoice_no = invoice_tree.item(item)['values'][1]

                # Check which column was clicked
                if invoice_tree.column(column, "id") == "Actions":
                    # Pay button clicked - check if user has permission
                    if limited_user:
                        messagebox.showinfo("Limited Access", "You do not have permission to make payments.\nPlease contact an administrator.")
                    else:
                        update_payment_from_tracking(invoice_no)
                elif invoice_tree.column(column, "id") == "Copy":
                    # Copy button clicked - all users can copy
                    copy_invoice_number(invoice_no)

    # Function to update payment from tracking page
    def update_payment_from_tracking(invoice_no):
        # Find the invoice in the Excel file
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
        excel_file = os.path.join(invoice_dir, "invoices.xlsx")

        if not os.path.exists(excel_file):
            messagebox.showerror("Error", "Invoices file not found")
            return

        try:
            # Load the Excel file first to get the current pending amount
            df = pd.read_excel(excel_file)

            # Find the invoice
            invoice_index = df[df["Invoice No"] == invoice_no].index
            if len(invoice_index) == 0:
                messagebox.showerror("Error", f"Invoice {invoice_no} not found in the file")
                return

            # Get current values
            current_received = df.loc[invoice_index[0], "Received Amount"]
            current_pending = df.loc[invoice_index[0], "Pending Amount"]

            # Check if pending amount is already 0
            if current_pending <= 0:
                messagebox.showinfo("Payment Not Needed",
                                  f"Invoice {invoice_no} has no pending amount to pay.\nIt should be marked as Cleared.")

                # Ensure status is set to Cleared
                df.loc[invoice_index[0], "Status"] = "Cleared"

                # Save with proper formatting
                with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Invoices')
                    apply_excel_formatting(writer, df)

                # Refresh the invoice tracking page
                create_invoice_tracking_content()
                return

            # Ask for the received amount with the maximum value set to the current pending amount
            received = simpledialog.askfloat("Update Payment",
                                           f"Enter received amount for Invoice {invoice_no}:\n(Maximum: ₹{current_pending:.2f})",
                                           minvalue=0,
                                           maxvalue=current_pending)
            if received is None:  # User cancelled
                return

            # Calculate new values
            new_received = current_received + received
            new_pending = current_pending - received

            # Update the received amount and pending amount
            df.loc[invoice_index[0], "Received Amount"] = new_received
            df.loc[invoice_index[0], "Pending Amount"] = new_pending

            # Update today's date as the payment date
            today = datetime.now().strftime("%d-%m-%Y")
            df.loc[invoice_index[0], "Payment Date"] = today

            # Update status and payment date based on pending amount
            if new_pending == 0:
                # If fully paid, mark as Cleared
                df.loc[invoice_index[0], "Status"] = "Cleared"
            else:
                # If partially paid, set payment date to 10 days from today
                today_date = datetime.now()
                next_payment_date = (today_date + timedelta(days=10)).strftime("%d-%m-%Y")
                df.loc[invoice_index[0], "Payment Date"] = next_payment_date
                df.loc[invoice_index[0], "Status"] = "Active"

                # Show message about next payment date
                messagebox.showinfo("Payment Date Updated",
                                  f"Next payment due date set to {next_payment_date}.\n"
                                  f"If not paid by this date, status will change to Expired.")

            # Save the Excel file with proper formatting
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Invoices')
                apply_excel_formatting(writer, df)

            # Refresh the invoice tracking page
            create_invoice_tracking_content()

            # Show success message with details and ask if user wants to download the invoice
            if new_pending == 0:
                download = messagebox.askyesno("Payment Successful",
                                  f"Payment of ₹{received:.2f} recorded for Invoice {invoice_no}.\n"
                                  f"Invoice is now fully paid and marked as Cleared.\n\n"
                                  f"Would you like to download the invoice?")
            else:
                download = messagebox.askyesno("Payment Successful",
                                  f"Payment of ₹{received:.2f} recorded for Invoice {invoice_no}.\n"
                                  f"Remaining pending amount: ₹{new_pending:.2f}\n"
                                  f"Status changed to Active.\n\n"
                                  f"Would you like to download the invoice?")

            # If user wants to download the invoice, find and open the PDF
            if download:
                # Find the invoice PDF file
                invoice_pdf_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice")
                invoice_pdf_file = None

                # Search for the invoice PDF file
                for root, _, files in os.walk(invoice_pdf_dir):
                    for file in files:
                        if file.lower().endswith('.pdf') and invoice_no in file:
                            invoice_pdf_file = os.path.join(root, file)
                            break
                    if invoice_pdf_file:
                        break

                # Open the invoice PDF file if found
                if invoice_pdf_file and os.path.exists(invoice_pdf_file):
                    try:
                        os.startfile(invoice_pdf_file)
                    except Exception as e:
                        messagebox.showerror("Error", f"Could not open invoice PDF: {str(e)}")
                else:
                    messagebox.showinfo("Invoice Not Found",
                                      f"Could not find PDF for Invoice {invoice_no}.\n"
                                      f"Please check the Invoice folder or generate the invoice again.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update payment: {str(e)}")

    # Bind left-click for actions column
    invoice_tree.bind("<Button-1>", on_actions_click)

# Function to apply Excel formatting
def apply_excel_formatting(writer, df):
    """
    Apply professional Excel formatting to the worksheet.

    Args:
        writer: The ExcelWriter object
        df: The DataFrame being written
    """
    # Get the worksheet
    worksheet = writer.sheets['Invoices']

    # Format headers
    header_font = Font(name='Segoe UI', size=11, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='2f3640', end_color='2f3640', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Style for data cells
    data_font = Font(name='Segoe UI', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    number_alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Add borders - thicker for better visibility
    medium_border = Side(style='medium')
    thin_border = Side(style='thin')

    # Create different border styles
    header_border = Border(
        left=thin_border,
        right=thin_border,
        top=medium_border,
        bottom=medium_border
    )

    data_border = Border(
        left=thin_border,
        right=thin_border,
        top=thin_border,
        bottom=thin_border
    )

    # Format headers
    for cell in worksheet[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = header_border

    # Apply alternating row colors for better readability
    even_fill = PatternFill(start_color='F9F9F9', end_color='F9F9F9', fill_type='solid')
    odd_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

    # Format data cells
    for row in range(2, len(df) + 2):  # +2 because Excel is 1-indexed and we have a header row
        # Apply alternating row colors
        row_fill = even_fill if row % 2 == 0 else odd_fill

        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=row, column=col)
            cell.font = data_font
            cell.border = data_border
            cell.fill = row_fill

            # Apply specific formatting based on column type
            col_name = df.columns[col-1]

            # Numbers and amounts - right aligned
            if col_name in ["Sr No", "Received Amount", "Pending Amount"]:
                cell.alignment = number_alignment
                if col_name in ["Received Amount", "Pending Amount"]:
                    cell.number_format = '#,##0.00'

            # Dates - center aligned
            elif col_name in ["Payment Date", "Invoice Date"]:
                cell.alignment = center_alignment
                cell.number_format = 'DD-MM-YYYY'

            # Status - center aligned
            elif col_name == "Status":
                cell.alignment = center_alignment

            # Text fields - left aligned
            else:
                cell.alignment = data_alignment

    # Add thick border around the entire table
    max_row = len(df) + 1  # +1 for header
    max_col = len(df.columns)

    # Top border of header row
    for col in range(1, max_col + 1):
        cell = worksheet.cell(row=1, column=col)
        cell.border = Border(
            left=cell.border.left,
            right=cell.border.right,
            top=medium_border,
            bottom=cell.border.bottom
        )

    # Bottom border of last row
    for col in range(1, max_col + 1):
        cell = worksheet.cell(row=max_row + 1, column=col)
        cell.border = Border(
            left=cell.border.left,
            right=cell.border.right,
            top=cell.border.top,
            bottom=medium_border
        )

    # Left border of first column
    for row in range(1, max_row + 2):
        cell = worksheet.cell(row=row, column=1)
        if cell.border:
            cell.border = Border(
                left=medium_border,
                right=cell.border.right,
                top=cell.border.top,
                bottom=cell.border.bottom
            )

    # Right border of last column
    for row in range(1, max_row + 2):
        cell = worksheet.cell(row=row, column=max_col)
        if cell.border:
            cell.border = Border(
                left=cell.border.left,
                right=medium_border,
                top=cell.border.top,
                bottom=cell.border.bottom
            )

    # Define status colors
    status_colors = {
        "Cleared": "0000FF",  # Blue
        "Active": "00FF00",   # Green
        "Expired": "FF0000"   # Red
    }

    # Find the status column index
    status_col_idx = None
    for idx, col_name in enumerate(df.columns):
        if col_name == "Status":
            status_col_idx = idx + 1  # +1 because Excel is 1-indexed
            break

    # Apply color formatting to status cells
    if status_col_idx:
        for row_idx, row in enumerate(df.iterrows(), 2):  # Start from row 2 (after header)
            status = row[1]["Status"]
            cell = worksheet.cell(row=row_idx, column=status_col_idx)

            # Set font color based on status
            if status in status_colors:
                cell.font = Font(color=status_colors[status], bold=True)

    # Set column widths
    column_widths = {
        "Sr No": 6,
        "Invoice No": 22,
        "Customer Name": 35,
        "Mobile": 18,
        "GST": 22,
        "Received Amount": 18,
        "Pending Amount": 18,
        "Payment Date": 15,
        "Invoice Date": 15,
        "Status": 12
    }

    # Apply column widths
    for col, width in column_widths.items():
        if col in df.columns:
            col_letter = get_column_letter(df.columns.get_loc(col) + 1)
            worksheet.column_dimensions[col_letter].width = width

    # Auto-filter for easy sorting and filtering
    worksheet.auto_filter.ref = f"A1:{get_column_letter(len(df.columns))}1"

    # Freeze the header row
    worksheet.freeze_panes = 'A2'

# Update all invoice statuses on application start
update_all_invoice_statuses()

# Show Dashboard by default
show_frame("Dashboard")

# Load profile preview but don't show the Profile tab
def show_profile_preview_on_start():
    global preview_data
    profile_file = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile", "company_profile.xlsx")

    if os.path.exists(profile_file):
        try:
            df = pd.read_excel(profile_file)
            if not df.empty:
                preview_data = df.iloc[0].to_dict()

                # Hide the form and show preview
                form_frame.pack_forget()
                button_frame.pack_forget()
                preview_frame.place(relx=0.05, rely=0.05, relwidth=0.9, relheight=0.85)

                # Update preview with saved data
                update_preview()

                # Don't show Profile tab - Dashboard will be shown by default
                print("Profile data loaded successfully")

        except Exception as e:
            print(f"Error loading profile preview: {e}")

# -------- Support & Help Tab --------
support_help_frame = frames["Support & Help"]
support_help_frame.pack_forget()

def create_support_help_content():
    # Clear any existing widgets
    for widget in support_help_frame.winfo_children():
        widget.destroy()

    # Create main layout
    support_help_layout = tk.Frame(support_help_frame, bg="white")
    support_help_layout.pack(fill="both", expand=True, padx=15, pady=10)

    # Title
    title_frame = tk.Frame(support_help_layout, bg="white")
    title_frame.pack(fill="x", pady=(0, 20))
    tk.Label(title_frame, text="Support & Help", font=("Segoe UI", 22, "bold"), bg="white").pack(anchor="w", padx=20)

    # Create left and right frames for side-by-side layout
    left_frame = tk.Frame(support_help_layout, bg="white", width=400)
    left_frame.pack(side="left", fill="y", padx=(20, 10), pady=10)
    left_frame.pack_propagate(False)  # Prevent frame from shrinking

    right_frame = tk.Frame(support_help_layout, bg="white")
    right_frame.pack(side="left", fill="both", expand=True, padx=(10, 20), pady=10)

    # ===== LEFT SIDE: SUPPORT SECTION =====
    support_frame = tk.Frame(left_frame, bg="white", relief="solid", borderwidth=1)
    support_frame.pack(fill="x", pady=(0, 20))

    # Support Header
    support_header = tk.Frame(support_frame, bg="#3498db")
    support_header.pack(fill="x")
    tk.Label(support_header, text="📞 SUPPORT", font=("Segoe UI", 16, "bold"),
             bg="#3498db", fg="white", padx=15, pady=10).pack(anchor="w")

    # Support Content
    support_content = tk.Frame(support_frame, bg="white")
    support_content.pack(fill="x", padx=15, pady=15)

    # Company Information
    company_info = [
        ("Company Name:", "AMSSoftX"),
        ("Contact No.:", "+91 7666405292"),
        ("Email:", "<EMAIL>"),
        ("Website:", "https://amssoftx.com")
    ]

    for i, (label, value) in enumerate(company_info):
        info_frame = tk.Frame(support_content, bg="white")
        info_frame.pack(fill="x", pady=8)

        tk.Label(info_frame, text=label, font=("Segoe UI", 12, "bold"),
                bg="white", width=15, anchor="w").pack(side="left")

        if label == "Website:":
            # Make website clickable
            website_label = tk.Label(info_frame, text=value, font=("Segoe UI", 12),
                                   bg="white", fg="#00a8ff", cursor="hand2")
            website_label.pack(side="left")

            # Add click functionality
            def open_website(event):
                import webbrowser
                webbrowser.open("https://amssoftx.com")

            website_label.bind("<Button-1>", open_website)
        elif label == "Email:":
            # Make email clickable
            email_label = tk.Label(info_frame, text=value, font=("Segoe UI", 12),
                                 bg="white", fg="#00a8ff", cursor="hand2")
            email_label.pack(side="left")

            # Add click functionality
            def open_email(event):
                import webbrowser
                webbrowser.open("mailto:<EMAIL>")

            email_label.bind("<Button-1>", open_email)
        else:
            tk.Label(info_frame, text=value, font=("Segoe UI", 12),
                   bg="white").pack(side="left")

    # ===== RIGHT SIDE: HELP SECTION =====
    # Create scrollable canvas for help content
    help_canvas = tk.Canvas(right_frame, bg="white")
    help_scrollbar = tk.Scrollbar(right_frame, orient="vertical", command=help_canvas.yview)
    help_scrollable_frame = tk.Frame(help_canvas, bg="white")

    help_scrollable_frame.bind(
        "<Configure>",
        lambda e: help_canvas.configure(scrollregion=help_canvas.bbox("all"))
    )

    help_canvas.create_window((0, 0), window=help_scrollable_frame, anchor="nw")
    help_canvas.configure(yscrollcommand=help_scrollbar.set)

    # Pack the canvas and scrollbar
    help_canvas.pack(side="left", fill="both", expand=True)
    help_scrollbar.pack(side="right", fill="y")

    # Help Header
    help_header = tk.Frame(help_scrollable_frame, bg="#2ecc71", relief="solid", borderwidth=1)
    help_header.pack(fill="x")
    tk.Label(help_header, text="❓ HELP & SOFTWARE GUIDE", font=("Segoe UI", 16, "bold"),
             bg="#2ecc71", fg="white", padx=15, pady=10).pack(anchor="w")

    # Help Content
    help_content = tk.Frame(help_scrollable_frame, bg="white", relief="solid", borderwidth=1)
    help_content.pack(fill="both", expand=True, pady=(0, 20))

    # Create detailed help content with sections
    sections = [
        {
            "title": "1. DASHBOARD",
            "content": """The dashboard is your central control panel that provides a quick overview of your business operations.

• Company Profile Section:
  - Displays your company name, logo, and contact information
  - Shows GST number, phone, email, and address in a professional layout

• Invoice Statistics:
  - Active Invoices: Shows count of invoices with pending payments within due date
  - Cleared Invoices: Shows count of fully paid invoices
  - Expired Invoices: Shows count of overdue invoices with pending payments
  - Total Customers: Shows the total number of customers in your database

• Low Stock Products:
  - Automatically displays products with stock below the set threshold
  - Color-coded in red for easy identification
  - Shows product name, current stock, and low stock alert level
  - Double-click to quickly navigate to product management for restocking

• Invoice Search:
  - Search for any invoice by invoice number
  - Displays results from all invoice formats (PDF, Excel, Word)
  - Click on results to open the invoice directly

• Expired Invoices Section:
  - Lists invoices with overdue payments
  - Shows customer details and pending amounts
  - Provides direct payment update functionality
  - Click on "more expired invoices" to navigate to Invoice Tracking

• Latest Invoices:
  - Shows your most recently created invoice PDFs
  - Click to open directly
  - Copy invoice numbers with right-click menu"""
        },
        {
            "title": "2. PROFILE MANAGEMENT",
            "content": """The Profile tab allows you to set up your company information that will appear on all invoices.

• Company Information:
  - Company Name: Appears prominently on invoices and dashboard
  - Logo: Upload your company logo (recommended size: 200x200 pixels)
  - GST Number: Automatically included on all invoices
  - Phone Number: Contact information for customers
  - Email: Business email for customer inquiries
  - Address: Full business address for invoices and legal documents
  - State: Required for GST calculations

• Bank Details:
  - Account Holder Name: Name on your business bank account
  - Account Number: Your business account number
  - IFSC Code: Bank routing code for payments
  - Branch: Local branch information

• Terms and Conditions:
  - Customizable terms that appear on all invoices
  - Default terms provided but fully editable
  - Payment terms, return policies, and legal disclaimers

• Company Stamp/Sign:
  - Upload your digital signature or company stamp
  - Automatically appears on all generated invoices
  - Recommended size: 200x100 pixels

• Preview Mode:
  - See how your information will appear on invoices
  - Test the layout before creating actual invoices
  - Make adjustments as needed"""
        },
        {
            "title": "3. PRODUCT MANAGEMENT",
            "content": """The Products tab provides comprehensive inventory management capabilities.

• Adding Products:
  - Product Name: Enter the full name of your product
  - Description: Detailed product description for invoices
  - HSN/SAC Code: Required tax code for GST
  - Price: Set the base price before taxes
  - GST %: Set the applicable GST percentage
  - Current Stock: Track available inventory
  - Low Stock Alert: Set threshold for dashboard warnings
  - Unit: Specify the unit of measurement (pcs, kg, etc.)

• Managing Products:
  - Edit: Update any product details as needed
  - Delete: Remove products no longer offered
  - Search: Quickly find products by name or description
  - Sort: Organize products by any column
  - Color Coding: Red for low stock, green for adequate stock

• Excel-like Interface:
  - Familiar spreadsheet layout for easy data entry
  - Professional formatting with alternating row colors
  - Clear column headers and proper alignment
  - Automatic calculations for tax amounts

• Stock Management:
  - Update stock levels when receiving new inventory
  - Automatic stock reduction when creating invoices
  - Low stock alerts appear on dashboard
  - Export product list to Excel for inventory audits"""
        },
        {
            "title": "4. CUSTOMER MANAGEMENT",
            "content": """The Customer tab helps you maintain a comprehensive database of your clients.

• Adding Customers:
  - Customer Name: Full business or individual name
  - Email: Primary contact email
  - Phone Number: Primary contact number
  - GST Number: Customer's GST registration (if applicable)
  - State: Required for interstate GST calculations
  - Address: Full billing address
  - Shipping To: Alternative shipping address if different

• Managing Customers:
  - Edit: Update any customer details as needed
  - Delete: Remove customers no longer active
  - Search: Quickly find customers by name, phone, or GST number
  - Sort: Organize customer list by any column

• Customer Selection:
  - Easily select customers when creating invoices
  - Customer details automatically populate invoice fields
  - Shipping address options based on saved information
  - Quick access to full customer history

• Data Validation:
  - Ensures required fields are completed
  - Validates phone number and email formats
  - Checks GST number format for accuracy
  - Prevents duplicate customer entries"""
        },
        {
            "title": "5. INVOICE CREATION",
            "content": """The Invoice tab provides a professional invoice generation system.

• Invoice Setup:
  - Automatic Invoice Number: Generated with date and time stamp
  - Refresh Button: Generate new invoice number if needed
  - Date Selection: Set custom invoice date or use current date
  - Customer Selection: Choose from your saved customers
  - All customer details automatically populate

• Product Selection:
  - Add multiple products from your inventory
  - Quantity adjustment with automatic calculations
  - Price adjustments possible for special discounts
  - Remove products as needed
  - Add new rows for additional products

• Calculations:
  - Subtotal: Automatic sum of all products
  - GST: Calculated based on product GST percentages
  - Grand Total: Final amount including all taxes
  - Amount in Words: Automatically generated for banking purposes

• Payment Terms:
  - Received Amount: Record initial payments
  - Balance/Pending: Automatically calculated
  - Payment Date: Set due date for remaining balance
  - Status: Automatically set based on payment terms

• Invoice Actions:
  - Save Invoice: Store in database for tracking
  - Download Invoice: Generate PDF, Excel, or Word format
  - Preview: See how the invoice will look before finalizing
  - Print: Send directly to printer

• Professional Formatting:
  - Company details and logo at the top
  - Customer information clearly displayed
  - Product table with proper alignment
  - Payment terms and conditions
  - Company stamp and signature"""
        },
        {
            "title": "6. INVOICE TRACKING",
            "content": """The Invoice Tracking tab helps you monitor payment status and manage outstanding invoices.

• Invoice Status Categories:
  - Active: Invoices with pending payments within the due date
  - Cleared: Fully paid invoices with no balance remaining
  - Expired: Overdue invoices with pending payments
  - All: Complete view of all invoices regardless of status

• Detailed Invoice Information:
  - Invoice Number: Unique identifier for each invoice
  - Customer Details: Name, contact information, and GST
  - Amount Information: Total, received, and pending amounts
  - Date Information: Invoice date and payment due date
  - Status: Visual indicator of payment status

• Payment Management:
  - Update Payments: Record new payments against invoices
  - Partial Payments: Track multiple payments for a single invoice
  - Payment Date Updates: Automatically adjusts based on payments
  - Status Updates: Automatically changes when payments are recorded

• Color Coding:
  - Active Invoices: Green for easy identification
  - Cleared Invoices: Blue to indicate completed transactions
  - Expired Invoices: Red to highlight urgent attention needed

• Filtering and Sorting:
  - Filter by status to focus on specific categories
  - Sort by any column (date, amount, customer, etc.)
  - Search functionality to find specific invoices
  - Date range filtering for periodic reviews

• Actions:
  - Copy Invoice Number: Quick access for reference
  - View Invoice: Open the generated invoice file
  - Update Payment: Record new payments
  - Print Statement: Generate payment history report"""
        },
        {
            "title": "7. SPECIAL FEATURES",
            "content": """AMS-InvoSync includes several special features to enhance your business operations.

• Automatic Status Updates:
  - Invoice status changes automatically based on payment date
  - Cleared status when full payment is received
  - Active status for invoices within payment period
  - Expired status for overdue invoices
  - Dashboard alerts for expired invoices

• Excel-like Interface:
  - Familiar spreadsheet layout throughout the application
  - Professional formatting with alternating row colors
  - Easy data entry and editing
  - Sortable columns and filtering options

• Color Coding System:
  - Invoice Status: Green (Active), Blue (Cleared), Red (Expired)
  - Product Stock: Red (Low), Green (Adequate)
  - Visual indicators make it easy to identify priorities

• Multi-format Invoices:
  - PDF: Professional printable format
  - Excel: Editable spreadsheet format
  - Word: Document format for further customization
  - All formats maintain professional layout and branding

• Application Refresh:
  - Header refresh button to restart application if needed
  - Clears any temporary issues or glitches
  - Ensures data consistency across all modules
  - Quick and convenient access from any screen

• Tooltips and Guidance:
  - Helpful tooltips on hover for many elements
  - Clear section headers and instructions
  - Consistent navigation throughout the application
  - Confirmation dialogs for important actions"""
        },
        {
            "title": "8. WORKFLOW GUIDE",
            "content": """Follow this step-by-step workflow guide for optimal use of AMS-InvoSync.

• Initial Setup:
  1. Set up your company profile with complete details
     - Add your company logo and stamp/signature
     - Enter all contact and GST information
     - Set up bank details for payment instructions
     - Customize terms and conditions

  2. Add your product inventory
     - Enter all products with accurate descriptions
     - Set correct prices and GST percentages
     - Define stock levels and low stock alerts
     - Organize products with proper units and codes

  3. Add your customers
     - Enter customer details with correct GST information
     - Add complete address and shipping information
     - Verify contact details for communication
     - Organize customers by category if needed

• Daily Operations:
  1. Start at the dashboard
     - Check for low stock alerts and reorder as needed
     - Review expired invoices and follow up on payments
     - Monitor invoice statistics for business health

  2. Create new invoices
     - Select customer from your database
     - Add products with correct quantities
     - Set appropriate payment terms
     - Generate and send invoice in preferred format

  3. Update payments
     - Record new payments as they are received
     - Update payment dates for partial payments
     - Monitor status changes based on payments
     - Follow up on expired invoices

• Periodic Maintenance:
  1. Update product information
     - Adjust prices as needed
     - Update stock levels after inventory checks
     - Add new products as your offerings expand
     - Archive discontinued products

  2. Maintain customer database
     - Update contact information as it changes
     - Add new customers as your business grows
     - Review customer purchase history
     - Identify key customers for special attention

  3. Review business performance
     - Analyze invoice statistics for trends
     - Monitor payment patterns and cash flow
     - Identify popular products and services
     - Plan inventory based on sales history"""
        },
        {
            "title": "9. TROUBLESHOOTING",
            "content": """If you encounter any issues while using AMS-InvoSync, try these troubleshooting steps.

• Application Issues:
  - If the application becomes unresponsive, use the refresh button in the header
  - Restart the application completely if refresh doesn't resolve the issue
  - Check that your system meets the minimum requirements
  - Ensure you have the latest version of the software

• Invoice Generation Problems:
  - Verify that your company profile is complete with all required information
  - Check that the selected customer has all necessary details
  - Ensure products have valid prices and GST percentages
  - Confirm that you have proper permissions to save files to the output directories

• Data Display Issues:
  - If tables appear empty, try refreshing the view
  - Check that you have added data to the relevant section
  - Verify that search filters aren't limiting your view
  - Ensure the correct tab is selected for the data you want to view

• Printing Problems:
  - Verify that your printer is properly connected and has paper
  - Check that the generated PDF is complete before printing
  - Try saving the file first, then printing from a PDF reader
  - Adjust printer settings for proper scaling if needed

• Getting Help:
  - For any persistent issues, contact AMSSoftX support
  - Provide detailed information about the problem you're experiencing
  - Include screenshots if possible to illustrate the issue
  - Note any error messages that appear

• Contact Support:
  - Email: <EMAIL>
  - Phone: +91 7666405292
  - Website: https://amssoftx.com"""
        }
    ]

    # Create each section with bold headers
    for i, section in enumerate(sections):
        section_frame = tk.Frame(help_content, bg="white")
        section_frame.pack(fill="x", padx=15, pady=(15 if i > 0 else 5))

        # Section title with bold, larger font
        title_label = tk.Label(section_frame, text=section["title"],
                             font=("Segoe UI", 14, "bold"),
                             bg="white", fg="#2c3e50")
        title_label.pack(anchor="w", pady=(0, 10))

        # Section content
        content_text = tk.Text(section_frame, wrap="word", font=("Segoe UI", 11),
                             bg="white", height=12, padx=5, pady=5,
                             relief="flat", borderwidth=0)
        content_text.pack(fill="x")
        content_text.insert("1.0", section["content"])
        content_text.config(state="disabled")  # Make it read-only

        # Add separator except for the last section
        if i < len(sections) - 1:
            separator = tk.Frame(help_content, height=2, bg="#ecf0f1")
            separator.pack(fill="x", padx=10, pady=5)

# Call this function on startup
show_profile_preview_on_start()

def edit_profile():
    # Check if user has limited access
    global user_type, limited_user
    if limited_user:
        messagebox.showinfo("Limited Access", "You do not have permission to edit company profile.\nPlease contact an administrator.")
        return

    # Show the form again
    preview_frame.place_forget()
    form_frame.pack(side="top", fill="x", padx=10)
    button_frame.pack(fill="x")

# Add Edit Profile button to preview frame
def update_preview():
    global logo_img, stamp_img
    for widget in preview_frame.winfo_children():
        widget.destroy()

    content_frame = tk.Frame(preview_frame, bg="#f0f0f0")
    content_frame.pack(anchor="nw", fill="both", expand=True)

    logo_frame = tk.Frame(content_frame, bg="#f0f0f0")
    logo_frame.pack(side="right", padx=20, anchor="n")

    if "Company Logo" in preview_data:
        try:
            img = Image.open(preview_data["Company Logo"])
            img = img.resize((120, 120))
            logo_img = ImageTk.PhotoImage(img)
            logo_label_img = tk.Label(logo_frame, image=logo_img, bg="#f0f0f0")
            logo_label_img.image = logo_img
            logo_label_img.pack()

            # Add logo label and delete button in a frame
            logo_label_frame = tk.Frame(logo_frame, bg="#f0f0f0")
            logo_label_frame.pack(pady=(2, 10))
            tk.Label(logo_label_frame, text="Company Logo", font=("Segoe UI", 9, "bold"),
                    bg="#f0f0f0").pack(side="left", padx=(0, 5))
            tk.Button(logo_label_frame, text="🗑️", command=delete_logo,
                     bg="#ff6b6b", fg="white", font=("Segoe UI", 8)).pack(side="left")

        except Exception as e:
            tk.Label(logo_frame, text="⚠️ Cannot load logo", bg="#f0f0f0", fg="red").pack()

    if "Stamp/Sign" in preview_data:
        try:
            img = Image.open(preview_data["Stamp/Sign"])
            img = img.resize((100, 100))
            stamp_img = ImageTk.PhotoImage(img)
            stamp_label_img = tk.Label(logo_frame, image=stamp_img, bg="#f0f0f0")
            stamp_label_img.image = stamp_img
            stamp_label_img.pack()

            # Add stamp label and delete button in a frame
            stamp_label_frame = tk.Frame(logo_frame, bg="#f0f0f0")
            stamp_label_frame.pack(pady=(2, 10))
            tk.Label(stamp_label_frame, text="Stamp / Sign", font=("Segoe UI", 9, "bold"),
                    bg="#f0f0f0").pack(side="left", padx=(0, 5))
            tk.Button(stamp_label_frame, text="🗑️", command=delete_stamp,
                     bg="#ff6b6b", fg="white", font=("Segoe UI", 8)).pack(side="left")

        except Exception as e:
            tk.Label(logo_frame, text="⚠️ Cannot load Stamp/Sign", bg="#f0f0f0", fg="red").pack()

    left_info = tk.Frame(content_frame, bg="#f0f0f0")
    left_info.pack(side="left", padx=30, anchor="n")

    right_info = tk.Frame(content_frame, bg="#f0f0f0")
    right_info.pack(side="left", padx=10, anchor="n")

    # Company & Contact Details
    tk.Label(left_info, text="Company & Contact Details", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w", pady=(0, 10))
    for key in ["Company Name", "Email", "Phone Number", "GST Number", "State", "Address"]:
        value = preview_data.get(key, "")
        tk.Label(left_info, text=f"{key}:", font=("Segoe UI", 9, "bold"), bg="#f0f0f0").pack(anchor="w")
        tk.Label(left_info, text=value, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w", pady=(0, 5))

    # Bank Details
    tk.Label(right_info, text="Bank Details", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w", pady=(0, 10))
    for key in ["Account Holder Name", "Account Number", "IFSC Code", "Branch"]:
        value = preview_data.get(key, "")
        tk.Label(right_info, text=f"{key}:", font=("Segoe UI", 9, "bold"), bg="#f0f0f0").pack(anchor="w")
        tk.Label(right_info, text=value, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w", pady=(0, 5))

    # Terms and Conditions
    terms_frame = tk.Frame(content_frame, bg="#f0f0f0")
    terms_frame.pack(fill="x", padx=30, pady=20)

    tk.Label(terms_frame, text="Terms and Conditions", font=("Segoe UI", 10, "bold"), bg="#f0f0f0").pack(anchor="w", pady=(0, 10))
    terms = preview_data.get("Terms and Conditions", "").split('\n')
    for term in terms:
        tk.Label(terms_frame, text=term, font=("Segoe UI", 9), bg="#f0f0f0").pack(anchor="w")

    # Add Edit Profile button at the bottom with modern styling
    edit_btn = create_modern_button(preview_frame, text="Edit Profile", command=edit_profile,
                                  bg_color=BTN_BG, fg_color="white", icon="✏️")
    edit_btn.pack(pady=20)

# Add these functions before update_preview
def delete_logo():
    global preview_data, logo_img
    if "Company Logo" in preview_data:
        del preview_data["Company Logo"]
        save_profile()
        update_preview()
        messagebox.showinfo("Success", "Company Logo deleted successfully!")

def delete_stamp():
    global preview_data, stamp_img
    if "Stamp/Sign" in preview_data:
        del preview_data["Stamp/Sign"]
        save_profile()
        update_preview()
        messagebox.showinfo("Success", "Stamp/Sign deleted successfully!")

def create_dashboard():
    # Create main dashboard frame
    dashboard_frame = frames["Dashboard"]

    # Clear all existing content
    for widget in dashboard_frame.winfo_children():
        widget.destroy()

    # Make sure customers_data is loaded
    if 'customers_data' not in globals():
        global customers_data
        load_customers()

    # No automatic refresh - dashboard will stay static until manually refreshed

    # Create a container frame to control width
    container = tk.Frame(dashboard_frame, bg="white")
    container.pack(fill="both", expand=True)

    # Create scrollable canvas for dashboard
    canvas = tk.Canvas(container, bg="white")
    scrollbar = tk.Scrollbar(container, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="white")

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # Pack the canvas and scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Main content frame with full width to use entire screen
    main_content = tk.Frame(scrollable_frame, bg="white")
    main_content.pack(fill="both", expand=True, padx=10, pady=10)

    # Create a frame for profile and statistics side by side
    top_section_frame = tk.Frame(main_content, bg="white")
    top_section_frame.pack(fill="x", padx=0, pady=2)

    # Profile Information Frame - Professional header style (left side) - wider
    profile_frame = tk.Frame(top_section_frame, bg="#f0f0f0", relief="solid", borderwidth=1)
    profile_frame.pack(side="left", fill="both", expand=True, padx=(0,5))

    # Load profile data
    profile_file = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile", "company_profile.xlsx")
    if os.path.exists(profile_file):
        try:
            df = pd.read_excel(profile_file)
            if not df.empty:
                profile_data = df.iloc[0].to_dict()

                # Professional header-style layout
                header_bg = "#f0f0f0"  # Light gray background

                # Top section with logo and company name - professional header style (wider)
                top_section = tk.Frame(profile_frame, bg=header_bg)
                top_section.pack(fill="x", padx=15, pady=10)

                # Left side - Logo with normal size
                logo_frame = tk.Frame(top_section, bg=header_bg)
                logo_frame.pack(side="left", padx=(0,15))

                if "Company Logo" in profile_data and os.path.exists(profile_data["Company Logo"]):
                    try:
                        img = Image.open(profile_data["Company Logo"])
                        img = img.resize((80, 80))  # Larger size for better visibility
                        logo_img = ImageTk.PhotoImage(img)
                        logo_label = tk.Label(logo_frame, image=logo_img, bg=header_bg)
                        logo_label.image = logo_img
                        logo_label.pack(padx=5, pady=5)
                    except:
                        tk.Label(logo_frame, text="Logo", font=("Segoe UI", 12), bg=header_bg, width=10, height=5).pack()

                # Middle - Company name and details
                details_frame = tk.Frame(top_section, bg=header_bg)
                details_frame.pack(side="left", fill="x", expand=True)

                # Company Name - Larger font for better visibility
                if "Company Name" in profile_data:
                    company_name = profile_data["Company Name"]
                    tk.Label(details_frame, text=f"{company_name}",
                            font=("Segoe UI", 16, "bold"), bg=header_bg, fg="#2c3e50").pack(anchor="w")

                # Using vertical layout for details - no need for info_frame

                # Details in vertical layout - one item per line

                # GST Number
                gst_frame = tk.Frame(details_frame, bg=header_bg)
                gst_frame.pack(fill="x", pady=2)
                tk.Label(gst_frame, text="GST No:", font=("Segoe UI", 9, "bold"),
                        bg=header_bg, fg="#2f3640").pack(side="left")
                tk.Label(gst_frame, text=profile_data.get("GST Number", "N/A"), font=("Segoe UI", 9),
                        bg=header_bg).pack(side="left", padx=5)

                # Phone Number
                phone_frame = tk.Frame(details_frame, bg=header_bg)
                phone_frame.pack(fill="x", pady=2)
                tk.Label(phone_frame, text="Phone:", font=("Segoe UI", 9, "bold"),
                        bg=header_bg, fg="#2f3640").pack(side="left")
                tk.Label(phone_frame, text=profile_data.get("Phone Number", "N/A"), font=("Segoe UI", 9),
                        bg=header_bg).pack(side="left", padx=5)

                # Email
                email_frame = tk.Frame(details_frame, bg=header_bg)
                email_frame.pack(fill="x", pady=2)
                tk.Label(email_frame, text="Email:", font=("Segoe UI", 9, "bold"),
                        bg=header_bg, fg="#2f3640").pack(side="left")
                tk.Label(email_frame, text=profile_data.get("Email", "N/A"), font=("Segoe UI", 9),
                        bg=header_bg).pack(side="left", padx=5)

                # Address
                address_frame = tk.Frame(details_frame, bg=header_bg)
                address_frame.pack(fill="x", pady=2)
                tk.Label(address_frame, text="Address:", font=("Segoe UI", 9, "bold"),
                        bg=header_bg, fg="#2f3640").pack(side="left")
                tk.Label(address_frame, text=profile_data.get("Address", "N/A"), font=("Segoe UI", 9),
                        bg=header_bg).pack(side="left", padx=5)
        except Exception as e:
            tk.Label(profile_frame, text="Error loading profile data",
                    font=("Segoe UI", 10), bg="#f0f0f0", fg="red").pack(pady=10)
    else:
        tk.Label(profile_frame, text="Please set up your company profile in the Profile tab",
                font=("Segoe UI", 10), bg="#f0f0f0").pack(pady=10)

    # Company info removed from bottom right as it's now in the header

    # Create a frame for the middle section (Low Stock Products and Invoice Search)
    middle_section_frame = tk.Frame(main_content, bg="white")
    middle_section_frame.pack(fill="x", padx=0, pady=2)

    # Low Stock Products Section - professional layout (left side) - wider
    low_stock_frame = tk.Frame(middle_section_frame, bg="white", relief="solid", borderwidth=1)
    low_stock_frame.pack(side="left", fill="both", expand=True, padx=(0,5), pady=0)

    # Invoice Search Section - professional layout (right side) - wider
    invoice_search_frame = tk.Frame(middle_section_frame, bg="white", relief="solid", borderwidth=1)
    invoice_search_frame.pack(side="right", fill="both", expand=True, padx=(5,0), pady=0)

    # Section title with icon - professional header style
    low_stock_title = tk.Frame(low_stock_frame, bg="#FF0000")
    low_stock_title.pack(fill="x")
    tk.Label(low_stock_title, text="⚠️ Low Stock Products", font=("Segoe UI", 11, "bold"),
             bg="#FF0000", fg="white", padx=10, pady=3).pack(anchor="w")

    # Create a frame for the low stock products table - professional layout
    low_stock_table_frame = tk.Frame(low_stock_frame, bg="white")
    low_stock_table_frame.pack(fill="x", padx=10, pady=8)

    # Create a Treeview for low stock products - better height for visibility
    low_stock_tree = ttk.Treeview(low_stock_table_frame,
                                 columns=("Sr", "Name", "Quantity", "Alert", "Status"),
                                 show="headings",
                                 style="Treeview",
                                 height=4)  # Show 4 rows for better visibility

    # Define columns - simplified headers
    low_stock_tree.heading("Sr", text="Sr.")
    low_stock_tree.heading("Name", text="Product Name")
    low_stock_tree.heading("Quantity", text="Qty")
    low_stock_tree.heading("Alert", text="Alert")
    low_stock_tree.heading("Status", text="Status")

    # Set column widths - more compact
    low_stock_tree.column("Sr", width=30, anchor="center")
    low_stock_tree.column("Name", width=200)
    low_stock_tree.column("Quantity", width=50, anchor="center")
    low_stock_tree.column("Alert", width=50, anchor="center")
    low_stock_tree.column("Status", width=70, anchor="center")

    # Configure tag for low stock
    low_stock_tree.tag_configure("low_stock", foreground="#FF0000", font=("Segoe UI", 10, "bold"))

    # Add scrollbar
    low_stock_scrollbar = ttk.Scrollbar(low_stock_table_frame, orient="vertical", command=low_stock_tree.yview)
    low_stock_tree.configure(yscrollcommand=low_stock_scrollbar.set)

    # Bind double-click event to edit product
    def on_low_stock_double_click(event):
        # Get selected item
        selected_item = low_stock_tree.focus()
        if not selected_item:
            return

        # Get the product name from the selected item
        product_name = low_stock_tree.item(selected_item)['values'][1]

        # Switch to Products tab
        show_frame("Products")

        # Find the product index in products_data
        for i, product in enumerate(products_data):
            if product.get("Product Name", "") == product_name:
                # Edit the product
                edit_product(i)
                break

    low_stock_tree.bind("<Double-1>", on_low_stock_double_click)

    # Pack the treeview and scrollbar
    low_stock_tree.pack(side="left", fill="x", expand=True)
    low_stock_scrollbar.pack(side="right", fill="y")

    # Populate low stock products
    low_stock_count = 0
    if os.path.exists(products_file):
        try:
            # Load products data if not already loaded
            if not products_data:
                df = pd.read_excel(products_file)
                temp_products_data = df.to_dict('records')
            else:
                temp_products_data = products_data

            # Filter for low stock products
            for i, product in enumerate(temp_products_data):
                try:
                    quantity = float(product.get("Quantity", 0))
                    low_stock = float(product.get("Low Stock Alert", 0))
                    if quantity <= low_stock:
                        low_stock_count += 1
                        values = [
                            str(low_stock_count),
                            product.get("Product Name", ""),
                            product.get("Quantity", ""),
                            product.get("Low Stock Alert", ""),
                            "⚠️ Low"
                        ]
                        item_id = low_stock_tree.insert("", "end", values=values, tags=("low_stock",))
                except:
                    # Skip invalid entries
                    pass
        except Exception as e:
            print(f"Error loading low stock products: {e}")

    # If no low stock products found, display a compact message
    if low_stock_count == 0:
        low_stock_tree.pack_forget()
        low_stock_scrollbar.pack_forget()
        tk.Label(low_stock_table_frame, text="No low stock products found",
                font=("Segoe UI", 9), bg="white", pady=3).pack()

    # No button for product management - automatic refresh

    # Invoice Search section is already created in the middle section

    # Section title with icon - professional header style (more compact)
    search_title = tk.Frame(invoice_search_frame, bg="#2980b9")
    search_title.pack(fill="x")
    tk.Label(search_title, text="🔍 Invoice Search", font=("Segoe UI", 10, "bold"),
             bg="#2980b9", fg="white", padx=5, pady=2).pack(anchor="w")

    # Create search interface - wider
    search_container = tk.Frame(invoice_search_frame, bg="white")
    search_container.pack(fill="x", padx=10, pady=5)

    # Search input and button
    search_input_frame = tk.Frame(search_container, bg="white")
    search_input_frame.pack(fill="x", pady=5)

    tk.Label(search_input_frame, text="Invoice Number:", font=("Segoe UI", 9, "bold"),
             bg="white").pack(side="left", padx=(0,5))

    # Search entry
    invoice_search_var = tk.StringVar()
    invoice_search_entry = tk.Entry(search_input_frame, textvariable=invoice_search_var,
                                  font=("Segoe UI", 9), width=20)
    invoice_search_entry.pack(side="left", padx=5)

    # Function to search for invoice PDF
    def search_invoice():
        invoice_num = invoice_search_var.get().strip()
        if not invoice_num:
            messagebox.showwarning("Search Error", "Please enter an invoice number")
            return

        # Clear previous results
        for widget in preview_frame.winfo_children():
            widget.destroy()

        # Search for invoice PDF
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice")

        # Check all subdirectories (PDF, Excel, Word)
        found_files = []
        for root, dirs, files in os.walk(invoice_dir):
            for file in files:
                if invoice_num in file:
                    found_files.append(os.path.join(root, file))

        if not found_files:
            tk.Label(preview_frame, text="No invoices found with that number",
                   font=("Segoe UI", 9), bg="white", fg="red").pack(pady=10)
            return

        # Display found files
        tk.Label(preview_frame, text=f"Found {len(found_files)} file(s):",
               font=("Segoe UI", 9, "bold"), bg="white").pack(anchor="w", pady=(5,2))

        for file_path in found_files:
            file_frame = tk.Frame(preview_frame, bg="white")
            file_frame.pack(fill="x", pady=2)

            # Get file type
            file_type = os.path.splitext(file_path)[1].upper()[1:]
            if file_type == "PDF":
                icon = "📄"
                color = "#e74c3c"
            elif file_type == "XLSX" or file_type == "XLS":
                icon = "📊"
                color = "#27ae60"
            elif file_type == "DOCX" or file_type == "DOC":
                icon = "📝"
                color = "#3498db"
            else:
                icon = "📎"
                color = "#7f8c8d"

            # File name with icon
            file_name = os.path.basename(file_path)
            file_label = tk.Label(file_frame, text=f"{icon} {file_name}",
                                font=("Segoe UI", 9), bg="white", fg=color,
                                cursor="hand2")
            file_label.pack(side="left", anchor="w")

            # Bind click event to open file
            file_label.bind("<Button-1>", lambda e, path=file_path: open_file(path))

    # Function to open file with default application
    def open_file(file_path):
        try:
            os.startfile(file_path)
        except Exception as e:
            messagebox.showerror("Error", f"Could not open file: {str(e)}")

    # Search button with modern styling
    search_button = create_modern_button(search_input_frame, text="Search",
                                       command=search_invoice,
                                       bg_color=INFO_COLOR, fg_color="white", icon="🔍")
    search_button.pack(side="left", padx=5, fill="y")

    # Preview frame - wider
    preview_frame = tk.Frame(search_container, bg="white", relief="solid", borderwidth=1)
    preview_frame.pack(fill="both", expand=True, pady=5)

    # Initial message - normal size
    tk.Label(preview_frame, text="Enter an invoice number and click Search",
           font=("Segoe UI", 9), bg="white").pack(pady=20)

    # Create a frame for right side sections
    right_sections_frame = tk.Frame(top_section_frame, bg="white")
    right_sections_frame.pack(side="right", fill="both", expand=True, padx=(2,0))

    # Top right section container
    top_right_frame = tk.Frame(right_sections_frame, bg="white")
    top_right_frame.pack(fill="x", expand=True)

    # Invoice Statistics Section - professional layout (right side of profile)
    invoice_stats_frame = tk.Frame(top_right_frame, bg="white", relief="solid", borderwidth=1)
    invoice_stats_frame.pack(side="left", fill="both", expand=True)

    # Section title with icon - professional header style
    invoice_stats_title = tk.Frame(invoice_stats_frame, bg="#3498db")
    invoice_stats_title.pack(fill="x")
    tk.Label(invoice_stats_title, text="📊 Invoice & Customer Statistics", font=("Segoe UI", 11, "bold"),
             bg="#3498db", fg="white", padx=10, pady=3).pack(anchor="w")

    # Latest Invoices Section - professional layout (far right) with proper width
    latest_invoices_frame = tk.Frame(top_right_frame, bg="white", relief="solid", borderwidth=1, width=200)
    latest_invoices_frame.pack(side="right", fill="y", expand=False, padx=(2,0))

    # Simple title for latest invoices - matching the image
    latest_invoices_title = tk.Frame(latest_invoices_frame, bg="#3498db", width=200)
    latest_invoices_title.pack(fill="x")
    tk.Label(latest_invoices_title, text="Latest Invoices", font=("Segoe UI", 9, "bold"),
             bg="#3498db", fg="white", padx=5, pady=2).pack(anchor="center")

    # Create a frame for the latest invoices list
    latest_invoices_container = tk.Frame(latest_invoices_frame, bg="white")
    latest_invoices_container.pack(fill="both", expand=True, padx=2, pady=2)

    # Function to get latest PDF invoice files only
    def get_latest_invoices():
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice")
        if not os.path.exists(invoice_dir):
            return []

        # Get only PDF invoice files from all subdirectories
        pdf_files = []
        for root, _, files in os.walk(invoice_dir):
            for file in files:
                if file.lower().endswith('.pdf'):
                    file_path = os.path.join(root, file)
                    pdf_files.append((file_path, os.path.getmtime(file_path)))

        # Sort by modification time (newest first) and take the top 5
        pdf_files.sort(key=lambda x: x[1], reverse=True)
        return [f[0] for f in pdf_files[:5]]

    # Function to copy invoice number to clipboard
    def copy_invoice_number(invoice_no):
        root.clipboard_clear()
        root.clipboard_append(invoice_no)
        messagebox.showinfo("Copied", f"Invoice number {invoice_no} copied to clipboard")

    # Function to open file with default application
    def open_invoice_file(file_path):
        try:
            os.startfile(file_path)
        except Exception as e:
            messagebox.showerror("Error", f"Could not open file: {str(e)}")

    # Function to show context menu for invoice file
    def show_invoice_context_menu(event, file_path):
        # Extract invoice number from filename
        file_name = os.path.basename(file_path)
        invoice_match = re.search(r'INV\d+', file_name)
        invoice_no = invoice_match.group(0) if invoice_match else ""

        # Create context menu
        context_menu = tk.Menu(root, tearoff=0, bg="#f5f6fa", fg="#2c3e50",
                              activebackground="#27ae60", activeforeground="white")

        # Add menu items
        context_menu.add_command(label="📋 Copy Invoice Number",
                               font=("Segoe UI", 9, "bold"),
                               command=lambda: copy_invoice_number(invoice_no))
        context_menu.add_command(label="📂 Open File",
                               font=("Segoe UI", 9, "bold"),
                               command=lambda: open_invoice_file(file_path))

        # Display context menu
        context_menu.post(event.x_root, event.y_root)

    # Get latest invoices
    latest_files = get_latest_invoices()

    if not latest_files:
        # Show message if no invoices found
        tk.Label(latest_invoices_container, text="No PDF invoices found",
               font=("Segoe UI", 9), bg="white").pack(pady=5)
    else:
        # Simple list of PDF files without scrollbar - matching the image
        for file_path in latest_files[:5]:  # Limit to 5 files
            file_name = os.path.basename(file_path)

            # File label with PDF icon - simple format
            file_label = tk.Label(latest_invoices_container, text=f"📄 {file_name}",
                                font=("Segoe UI", 8), bg="white", fg="#e74c3c",
                                cursor="hand2", anchor="w", padx=2)
            file_label.pack(fill="x", pady=1, anchor="w")

            # Bind right-click event to show context menu
            file_label.bind("<Button-3>", lambda e, path=file_path: show_invoice_context_menu(e, path))

            # Bind left-click event to open file
            file_label.bind("<Button-1>", lambda e, path=file_path: open_invoice_file(path))

    # Create a frame for the statistics - professional layout
    stats_container = tk.Frame(invoice_stats_frame, bg="white")
    stats_container.pack(fill="x", padx=10, pady=8)

    # Create a frame for each row of statistics - better spacing
    stats_row1 = tk.Frame(stats_container, bg="white")
    stats_row1.pack(fill="x", pady=3)

    stats_row2 = tk.Frame(stats_container, bg="white")
    stats_row2.pack(fill="x", pady=3)

    # Initialize statistics counters
    active_count = 0
    cleared_count = 0
    expired_count = 0

    # Make sure customers_data is loaded
    # Force reload customers data from Excel file
    global customers_data
    customers_data = []

    # Use the same customer_file path defined in the global variables
    if os.path.exists(customer_file):
        try:
            df = pd.read_excel(customer_file)
            customers_data = df.to_dict('records')
            print(f"Loaded {len(customers_data)} customers from Excel: {customer_file}")
        except Exception as e:
            print(f"Error loading customers: {e}")
    else:
        print(f"Customer file not found: {customer_file}")

        # Try to create the directory if it doesn't exist
        try:
            os.makedirs(os.path.dirname(customer_file), exist_ok=True)
        except Exception as e:
            print(f"Error creating customer directory: {e}")

    # Get invoice statistics from Excel file
    invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
    excel_file = os.path.join(invoice_dir, "invoices.xlsx")

    # Count customers from Customer.xlsx file
    customer_count = 0

    # Use the same customer_file path defined in the global variables
    if os.path.exists(customer_file):
        try:
            # Load customer data
            customer_df = pd.read_excel(customer_file)

            if not customer_df.empty:
                # Count total customers
                customer_count = len(customer_df)
                print(f"Found {customer_count} customers in Customer.xlsx file")
            else:
                print("Customer file exists but has no data")
                customer_count = 0
        except Exception as e:
            print(f"Error counting customers from Customer.xlsx: {e}")
            customer_count = 0
    else:
        print(f"Customer file not found: {customer_file}")
        customer_count = 0

    # Reset all counts to 0 if the file doesn't exist or there's an error
    active_count = 0
    cleared_count = 0
    expired_count = 0

    if os.path.exists(excel_file):
        try:
            # Load invoice data
            df = pd.read_excel(excel_file)

            if not df.empty:
                # Count invoices by status
                status_counts = df['Status'].value_counts().to_dict()
                active_count = status_counts.get('Active', 0)
                cleared_count = status_counts.get('Cleared', 0)
                expired_count = status_counts.get('Expired', 0)
        except Exception as e:
            print(f"Error loading invoice statistics: {e}")
            # Ensure counts are reset to 0 if there's an error
            active_count = 0
            cleared_count = 0
            expired_count = 0

    # Create statistics boxes - professional layout with better styling
    # Active Invoices
    active_frame = tk.Frame(stats_row1, bg="#f5f5f5", relief="solid", borderwidth=1, width=150, height=60)
    active_frame.pack(side="left", padx=5, fill="both", expand=True)
    active_frame.pack_propagate(False)
    tk.Label(active_frame, text="Active Invoices", font=("Segoe UI", 9, "bold"), bg="#f5f5f5").pack(pady=(5,0))
    tk.Label(active_frame, text=str(active_count), font=("Segoe UI", 16, "bold"), fg="#00AA00", bg="#f5f5f5").pack()

    # Cleared Invoices
    cleared_frame = tk.Frame(stats_row1, bg="#f5f5f5", relief="solid", borderwidth=1, width=150, height=60)
    cleared_frame.pack(side="left", padx=5, fill="both", expand=True)
    cleared_frame.pack_propagate(False)
    tk.Label(cleared_frame, text="Cleared Invoices", font=("Segoe UI", 9, "bold"), bg="#f5f5f5").pack(pady=(5,0))
    tk.Label(cleared_frame, text=str(cleared_count), font=("Segoe UI", 16, "bold"), fg="#0000AA", bg="#f5f5f5").pack()

    # Second row
    # Expired Invoices
    expired_frame = tk.Frame(stats_row2, bg="#f5f5f5", relief="solid", borderwidth=1, width=150, height=60)
    expired_frame.pack(side="left", padx=5, fill="both", expand=True)
    expired_frame.pack_propagate(False)
    tk.Label(expired_frame, text="Expired Invoices", font=("Segoe UI", 9, "bold"), bg="#f5f5f5").pack(pady=(5,0))
    tk.Label(expired_frame, text=str(expired_count), font=("Segoe UI", 16, "bold"), fg="#AA0000", bg="#f5f5f5").pack()

    # Total Customers
    customer_frame = tk.Frame(stats_row2, bg="#f5f5f5", relief="solid", borderwidth=1, width=150, height=60)
    customer_frame.pack(side="left", padx=5, fill="both", expand=True)
    customer_frame.pack_propagate(False)
    tk.Label(customer_frame, text="Total Customers", font=("Segoe UI", 9, "bold"), bg="#f5f5f5").pack(pady=(5,0))
    tk.Label(customer_frame, text=str(customer_count), font=("Segoe UI", 16, "bold"), fg="#9b59b6", bg="#f5f5f5").pack()

    # No buttons for invoice statistics - automatic refresh

    # Add Expired Invoices Alert section (full width)
    expired_invoices_frame = tk.Frame(main_content, bg="white", relief="solid", borderwidth=1)
    expired_invoices_frame.pack(fill="x", padx=0, pady=2)

    # Section title with icon - professional header style
    expired_title = tk.Frame(expired_invoices_frame, bg="#e74c3c")
    expired_title.pack(fill="x")

    # Create a clickable title that navigates to Invoice Tracking page
    title_label = tk.Label(expired_title, text="⚠️ Expired Invoices", font=("Segoe UI", 11, "bold"),
                         bg="#e74c3c", fg="white", cursor="hand2", padx=10, pady=3)
    title_label.pack(anchor="w")
    title_label.bind("<Button-1>", lambda e: show_frame("Invoice Tracking"))

    # Create a frame for the alert content
    alert_frame = tk.Frame(expired_invoices_frame, bg="white")
    alert_frame.pack(fill="x", padx=10, pady=8)

    # Function to update received amount
    def update_received_amount(invoice_no):
        # Find the invoice in the Excel file
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
        excel_file = os.path.join(invoice_dir, "invoices.xlsx")

        if not os.path.exists(excel_file):
            messagebox.showerror("Error", "Invoices file not found")
            return

        try:
            # Load the Excel file first to get the current pending amount
            df = pd.read_excel(excel_file)

            # Find the invoice
            invoice_index = df[df["Invoice No"] == invoice_no].index
            if len(invoice_index) == 0:
                messagebox.showerror("Error", f"Invoice {invoice_no} not found in the file")
                return

            # Get current values
            current_received = df.loc[invoice_index[0], "Received Amount"]
            current_pending = df.loc[invoice_index[0], "Pending Amount"]

            # Check if pending amount is already 0
            if current_pending <= 0:
                messagebox.showinfo("Payment Not Needed",
                                  f"Invoice {invoice_no} has no pending amount to pay.\nIt should be marked as Cleared.")

                # Ensure status is set to Cleared
                df.loc[invoice_index[0], "Status"] = "Cleared"
                df.to_excel(excel_file, index=False)

                # Refresh the expired invoices alert
                load_expired_invoices_alert()
                return

            # Ask for the received amount with the maximum value set to the current pending amount
            received = simpledialog.askfloat("Update Payment",
                                           f"Enter received amount for Invoice {invoice_no}:\n(Maximum: ₹{current_pending:.2f})",
                                           minvalue=0,
                                           maxvalue=current_pending)
            if received is None:  # User cancelled
                return

            # Calculate new values
            new_received = current_received + received
            new_pending = current_pending - received

            # Update the received amount and pending amount
            df.loc[invoice_index[0], "Received Amount"] = new_received
            df.loc[invoice_index[0], "Pending Amount"] = new_pending

            # Update today's date as the payment date
            today = datetime.now().strftime("%d-%m-%Y")
            df.loc[invoice_index[0], "Payment Date"] = today

            # Update status and payment date based on pending amount
            if new_pending == 0:
                # If fully paid, mark as Cleared
                df.loc[invoice_index[0], "Status"] = "Cleared"
            else:
                # If partially paid, set payment date to 10 days from today
                today_date = datetime.now()
                next_payment_date = (today_date + timedelta(days=10)).strftime("%d-%m-%Y")
                df.loc[invoice_index[0], "Payment Date"] = next_payment_date
                df.loc[invoice_index[0], "Status"] = "Active"

                # Show message about next payment date
                messagebox.showinfo("Payment Date Updated",
                                  f"Next payment due date set to {next_payment_date}.\n"
                                  f"If not paid by this date, status will change to Expired.")

            # Save the Excel file with proper formatting
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Invoices')
                apply_excel_formatting(writer, df)

            # Refresh the expired invoices alert
            load_expired_invoices_alert()

            # Show success message with details and ask if user wants to download the invoice
            if new_pending == 0:
                download = messagebox.askyesno("Payment Successful",
                                  f"Payment of ₹{received:.2f} recorded for Invoice {invoice_no}.\n"
                                  f"Invoice is now fully paid and marked as Cleared.\n\n"
                                  f"Would you like to download the invoice?")
            else:
                download = messagebox.askyesno("Payment Successful",
                                  f"Payment of ₹{received:.2f} recorded for Invoice {invoice_no}.\n"
                                  f"Remaining pending amount: ₹{new_pending:.2f}\n"
                                  f"Status changed to Active.\n\n"
                                  f"Would you like to download the invoice?")

            # If user wants to download the invoice, find and open the PDF
            if download:
                # Find the invoice PDF file
                invoice_pdf_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice")
                invoice_pdf_file = None

                # Search for the invoice PDF file
                for root, _, files in os.walk(invoice_pdf_dir):
                    for file in files:
                        if file.lower().endswith('.pdf') and invoice_no in file:
                            invoice_pdf_file = os.path.join(root, file)
                            break
                    if invoice_pdf_file:
                        break

                # Open the invoice PDF file if found
                if invoice_pdf_file and os.path.exists(invoice_pdf_file):
                    try:
                        os.startfile(invoice_pdf_file)
                    except Exception as e:
                        messagebox.showerror("Error", f"Could not open invoice PDF: {str(e)}")
                else:
                    messagebox.showinfo("Invoice Not Found",
                                      f"Could not find PDF for Invoice {invoice_no}.\n"
                                      f"Please check the Invoice folder or generate the invoice again.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update payment: {str(e)}")

    # Function to load expired invoices alert
    def load_expired_invoices_alert():
        # Clear existing widgets
        for widget in alert_frame.winfo_children():
            widget.destroy()

        # Load invoice data
        invoice_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Invoice", "Data")
        excel_file = os.path.join(invoice_dir, "invoices.xlsx")

        if not os.path.exists(excel_file):
            tk.Label(alert_frame, text="Invoices file not found",
                   font=("Segoe UI", 9), bg="white", fg="red").pack(pady=10)
            return

        try:
            # Load invoice data
            df = pd.read_excel(excel_file)

            # Filter for expired invoices
            expired_df = df[df["Status"] == "Expired"]

            if expired_df.empty:
                tk.Label(alert_frame, text="No expired invoices found",
                       font=("Segoe UI", 11), bg="white").pack(pady=10)
                return

            # No count display - directly show the table

            # Create a frame for the expired invoices table
            expired_table_frame = tk.Frame(alert_frame, bg="white")
            expired_table_frame.pack(fill="x", padx=5, pady=5)

            # Create a Treeview for expired invoices
            expired_tree = ttk.Treeview(expired_table_frame,
                                      columns=("Sr", "Invoice", "Copy", "Customer", "GST", "Mobile", "Pending", "Status"),
                                      show="headings",
                                      style="Treeview",
                                      height=4)  # Show 4 rows for better visibility

            # Define columns
            expired_tree.heading("Sr", text="Sr.")
            expired_tree.heading("Invoice", text="Invoice No")
            expired_tree.heading("Copy", text="Copy")
            expired_tree.heading("Customer", text="Customer Name")
            expired_tree.heading("GST", text="GST")
            expired_tree.heading("Mobile", text="Mobile")
            expired_tree.heading("Pending", text="Pending Amount")
            expired_tree.heading("Status", text="Status")

            # Set column widths
            expired_tree.column("Sr", width=30, anchor="center")
            expired_tree.column("Invoice", width=120, anchor="center")
            expired_tree.column("Copy", width=40, anchor="center")
            expired_tree.column("Customer", width=150)
            expired_tree.column("GST", width=100, anchor="center")
            expired_tree.column("Mobile", width=100, anchor="center")
            expired_tree.column("Pending", width=100, anchor="center")
            expired_tree.column("Status", width=70, anchor="center")

            # Configure tag for expired invoices
            expired_tree.tag_configure("expired", foreground="#FF0000", font=("Segoe UI", 9, "bold"))

            # Add scrollbar
            expired_scrollbar = ttk.Scrollbar(expired_table_frame, orient="vertical", command=expired_tree.yview)
            expired_tree.configure(yscrollcommand=expired_scrollbar.set)

            # Function to copy invoice number to clipboard
            def copy_invoice_number(invoice_no):
                root.clipboard_clear()
                root.clipboard_append(invoice_no)
                messagebox.showinfo("Copied", f"Invoice number {invoice_no} copied to clipboard")

            # Function to handle clicks on the tree
            def on_expired_tree_click(event):
                region = expired_tree.identify_region(event.x, event.y)
                if region == "cell":
                    column = expired_tree.identify_column(event.x)
                    item = expired_tree.identify_row(event.y)
                    if item:
                        # Get the invoice number from the selected item
                        invoice_no = expired_tree.item(item)['values'][1]

                        # Check which column was clicked
                        if expired_tree.column(column, "id") == "Copy":
                            # Copy button clicked
                            copy_invoice_number(invoice_no)

            # Function to navigate to Invoice Tracking on double-click
            def on_expired_tree_double_click(event):
                show_frame("Invoice Tracking")

            # Bind click and double-click events
            expired_tree.bind("<Button-1>", on_expired_tree_click)
            expired_tree.bind("<Double-1>", on_expired_tree_double_click)

            # Pack the treeview and scrollbar
            expired_tree.pack(side="left", fill="x", expand=True)
            expired_scrollbar.pack(side="right", fill="y")

            # Add expired invoices to the treeview
            for i, (_, row) in enumerate(expired_df.iterrows(), 1):
                values = [
                    str(i),
                    row["Invoice No"],
                    "📋",
                    row["Customer Name"],
                    row["GST"] if "GST" in row and pd.notna(row["GST"]) else "",
                    row["Mobile"] if "Mobile" in row and pd.notna(row["Mobile"]) else "",
                    f"₹{row['Pending Amount']:.2f}",
                    row["Status"]
                ]
                expired_tree.insert("", "end", values=values, tags=("expired",))

            # If there are more than 4 expired invoices, show a message
            if len(expired_df) > 4:
                more_frame = tk.Frame(alert_frame, bg="white")
                more_frame.pack(fill="x", pady=2)

                more_label = tk.Label(more_frame,
                                   text=f"+ {len(expired_df) - 4} more expired invoices...",
                                   font=("Segoe UI", 8), fg="#3498db", bg="white", cursor="hand2")
                more_label.pack(side="right", padx=10)
                more_label.bind("<Button-1>", lambda e: show_frame("Invoice Tracking"))

        except Exception as e:
            tk.Label(alert_frame, text=f"Error loading expired invoices: {str(e)}",
                   font=("Segoe UI", 9), bg="white", fg="red").pack(pady=10)

    # Load expired invoices alert
    load_expired_invoices_alert()

    # Make sure the dashboard frame is visible
    dashboard_frame.pack(fill="both", expand=True)

# Function to apply user permissions based on user type
def apply_user_permissions(user_role):
    global limited_user

    if user_role == "limited":
        # Set limited_user flag
        limited_user = True

        # Show product and customer preview sections for limited users
        try:
            products_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
            customers_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
        except:
            pass

        # Disable edit buttons and functionality for limited users
        # This will prevent them from making changes to the data

        # Only disable update buttons for products, allow add and clear buttons
        for widget in products_form.winfo_children():
            if isinstance(widget, tk.Button) and "Update" in widget.cget("text"):
                widget.config(state="disabled")
            # Make sure Add and Clear buttons are enabled
            elif isinstance(widget, tk.Button) and ("Add" in widget.cget("text") or "Clear" in widget.cget("text")):
                widget.config(state="normal")

        # Find the clear button in the product form and make sure it's enabled
        for widget in products_form.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.Button) and "Clear" in child.cget("text"):
                        child.config(state="normal")

        # Only disable update buttons for customers, allow add and clear buttons
        try:
            for widget in customer_form.winfo_children():
                if isinstance(widget, tk.Button):
                    if "Update" in widget.cget("text"):
                        widget.config(state="disabled")
                        print(f"Disabled button: {widget.cget('text')}")
                    elif "Add" in widget.cget("text") or "Clear" in widget.cget("text"):
                        widget.config(state="normal")
                        print(f"Enabled button: {widget.cget('text')}")
        except Exception as e:
            print(f"Error configuring buttons in customer form: {str(e)}")

        # Find the add and clear buttons in the customer form and make sure they're enabled
        try:
            for widget in customer_form.winfo_children():
                if isinstance(widget, tk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Button):
                            if "Add" in child.cget("text") or "Clear" in child.cget("text"):
                                child.config(state="normal")
                                print(f"Enabled nested button: {child.cget('text')}")
        except Exception as e:
            print(f"Error configuring nested buttons in customer form: {str(e)}")

        # Explicitly enable the add and clear buttons
        try:
            add_button.config(state="normal")
            clear_button.config(state="normal")
            print(f"Explicitly enabled add button: {add_button.cget('state')}")
            print(f"Explicitly enabled clear button: {clear_button.cget('state')}")
        except Exception as e:
            print(f"Could not explicitly enable add and clear buttons: {str(e)}")

        # Force update the UI
        try:
            root.update_idletasks()
        except Exception as e:
            print(f"Error updating UI: {str(e)}")

        # Disable profile edit button if it exists
        try:
            # Look for the edit profile button by text
            for widget in frames["Profile"].winfo_children():
                if isinstance(widget, tk.Button) and "Edit Profile" in widget.cget("text"):
                    widget.config(state="disabled")
                    print("Disabled Edit Profile button")
                    break
        except Exception as e:
            print(f"Could not disable Edit Profile button: {str(e)}")

        print(f"Applied limited permissions for user role: {user_role}")
    else:
        # Admin has full access
        limited_user = False

        # Make sure preview sections are visible for admin users
        try:
            products_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
            customers_preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
        except:
            pass

        print(f"Applied full permissions for user role: {user_role}")

# Set application icon
app_icon = "icon.ico"
if os.path.exists(app_icon):
    try:
        import ctypes
        # Set taskbar icon
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("amssoftx.amsinvosync.1.0")
    except:
        pass

# Check if software is activated
if not show_activation_window():
    print("Software not activated. Exiting...")
    sys.exit(0)

# Check if this is the first run by checking if a special flag file exists
profile_dir = os.path.join("D:\\", "AMSSoftX", "Softwares", "AMS-InvoSync", "Data", "Profile")
os.makedirs(profile_dir, exist_ok=True)
profile_file = os.path.join(profile_dir, "company_profile.xlsx")
first_run_flag_file = os.path.join(profile_dir, "setup_complete.flag")

# If the flag file exists, it means the software has been set up before
if os.path.exists(first_run_flag_file):
    # Not first run - show login window
    # Don't create users sheet here - it might overwrite existing users
    show_login_window()
else:
    # Check if users already exist (as a backup check)
    users_exist = False
    if os.path.exists(profile_file):
        try:
            wb = load_workbook(profile_file)
            if "Users" in wb.sheetnames and wb["Users"].max_row > 1:
                # Check if there's at least one user
                users_sheet = wb["Users"]
                for row in range(2, users_sheet.max_row + 1):
                    if users_sheet.cell(row=row, column=1).value:
                        users_exist = True
                        break
        except Exception as e:
            print(f"Error checking users: {e}")

    if users_exist:
        # Users exist but flag file doesn't - create flag file and show login
        with open(first_run_flag_file, 'w') as f:
            f.write("Setup completed")
        show_login_window()
    else:
        # First run - show signup window
        show_signup_window()

# Start the main loop
root.mainloop()
