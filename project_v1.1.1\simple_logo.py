import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import os

# Create a new image with a white background
width, height = 200, 200
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Draw a blue rectangle for the background
draw.rectangle([(0, 0), (width, height)], fill=(41, 128, 185))

# Draw text
try:
    # Try to use Arial font
    font_large = ImageFont.truetype("arial.ttf", 40)
    font_small = ImageFont.truetype("arial.ttf", 14)
except:
    # Fall back to default font
    font_large = ImageFont.load_default()
    font_small = ImageFont.load_default()

# Draw the text
draw.text((width//2, 80), "AMS", fill=(255, 255, 255), anchor="mm")
draw.text((width//2, 120), "InvoSync", fill=(255, 255, 255), anchor="mm")
draw.text((width//2, 160), "by AMSSoftX", fill=(255, 255, 255), anchor="mm")

# Save the image
image.save("icon.png")
print("Logo created: icon.png")

# Try to convert to ICO
try:
    # Resize for icon
    icon_image = image.resize((32, 32))
    icon_image.save("icon.ico")
    print("Icon created: icon.ico")
except Exception as e:
    print(f"Could not create icon: {e}")
