import tkinter as tk
from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 200, 200
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors
primary_color = (41, 128, 185)  # Blue
secondary_color = (52, 152, 219)  # Lighter blue
accent_color = (231, 76, 60)    # Red
white = (255, 255, 255)
dark_gray = (52, 73, 94)

# Draw a document shape (invoice)
# Main document background
draw.rectangle([(20, 20), (width-20, height-20)], fill=white, outline=primary_color, width=3)

# Header bar
draw.rectangle([(20, 20), (width-20, 50)], fill=primary_color)

# Draw line items (table)
y_pos = 70
# Table header
draw.rectangle([(30, y_pos), (width-30, y_pos+20)], fill=secondary_color)

# Table rows
for i in range(4):
    y_row = y_pos + 25 + (i * 20)
    # Alternating row colors
    if i % 2 == 0:
        draw.rectangle([(30, y_row), (width-30, y_row+20)], fill=(240, 240, 240))
    else:
        draw.rectangle([(30, y_row), (width-30, y_row+20)], fill=(230, 230, 230))

# Draw total section
y_total = y_pos + 110
draw.line([(width//2, y_total), (width-30, y_total)], fill=dark_gray, width=2)
draw.rectangle([(width-80, y_total+10), (width-30, y_total+30)], fill=accent_color)

# Save the image
image.save("invoice_icon.png")

# Create smaller versions for the icon
icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
icon_images = []

for size in icon_sizes:
    resized_img = image.resize(size, Image.LANCZOS)
    icon_images.append(resized_img)

# Save as ICO
icon_images[0].save("icon.ico", format="ICO", sizes=[(img.width, img.height) for img in icon_images])

print("Invoice icon created successfully!")
print("Files generated: invoice_icon.png and icon.ico")
