@echo off
echo =====================================================
echo                AMS-InvoSync
echo          इन्व्हॉइस मॅनेजमेंट सॉफ्टवेअर
echo                 by AMSSoftX
echo =====================================================
echo.
echo इन्स्टॉलेशन सुरू होत आहे...
echo.

REM Create required directories
echo डायरेक्टरीज तयार करत आहे...
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Profile" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Products" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Customer" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Invoice\Data" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Invoice\PDF" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Invoice\Excel" 2>nul
mkdir "D:\AMSSoftX\Softwares\AMS-InvoSync\Data\Invoice\Word" 2>nul
mkdir "C:\pukcab_XtfoSSMA\xtossmafiles\requried files\pukcab" 2>nul

REM Copy files
echo फाइल्स कॉपी करत आहे...
copy "AMS-InvoSync.exe" "D:\AMSSoftX\Softwares\AMS-InvoSync\" >nul
copy "icon.ico" "D:\AMSSoftX\Softwares\AMS-InvoSync\" >nul
copy "icon.png" "D:\AMSSoftX\Softwares\AMS-InvoSync\" >nul
copy "README.txt" "D:\AMSSoftX\Softwares\AMS-InvoSync\" >nul

REM Create desktop shortcut
echo डेस्कटॉप शॉर्टकट तयार करत आहे...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = oWS.SpecialFolders("Desktop") ^& "\AMS-InvoSync.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "D:\AMSSoftX\Softwares\AMS-InvoSync\AMS-InvoSync.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "D:\AMSSoftX\Softwares\AMS-InvoSync" >> CreateShortcut.vbs
echo oLink.IconLocation = "D:\AMSSoftX\Softwares\AMS-InvoSync\icon.ico" >> CreateShortcut.vbs
echo oLink.Description = "AMS-InvoSync by AMSSoftX" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript //nologo CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo इन्स्टॉलेशन पूर्ण झाले!
echo.
echo AMS-InvoSync सॉफ्टवेअर सुरू करण्यासाठी डेस्कटॉपवरील शॉर्टकट वापरा.
echo.
echo लायसन्स की: AMS-2319-2025
echo.
echo धन्यवाद!
echo.
pause
