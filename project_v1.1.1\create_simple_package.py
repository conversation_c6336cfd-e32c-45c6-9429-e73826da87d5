import os
import zipfile
import shutil

def create_simple_package():
    """Create a simple zip package with required files for AMS-InvoSync"""
    print("Creating AMS-InvoSync simple installation package...")
    
    # Files to include in the package
    files_to_include = [
        "icon.ico",
        "icon.png",
        "README_FULL.txt",
        "setup_exe.bat"
    ]
    
    # Create a temporary directory for packaging
    temp_dir = "AMS-InvoSync-Package"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Copy files to the temporary directory
    for file in files_to_include:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(temp_dir, file))
            print(f"Added {file} to package")
        else:
            print(f"Warning: {file} not found, skipping")
    
    # Rename README_FULL.txt to README.txt
    if os.path.exists(os.path.join(temp_dir, "README_FULL.txt")):
        os.rename(
            os.path.join(temp_dir, "README_FULL.txt"),
            os.path.join(temp_dir, "README.txt")
        )
        print("Renamed README_FULL.txt to README.txt")
    
    # Rename setup_exe.bat to setup.bat
    if os.path.exists(os.path.join(temp_dir, "setup_exe.bat")):
        os.rename(
            os.path.join(temp_dir, "setup_exe.bat"),
            os.path.join(temp_dir, "setup.bat")
        )
        print("Renamed setup_exe.bat to setup.bat")
    
    # Create a placeholder file for the .exe
    with open(os.path.join(temp_dir, "PLACE_EXE_HERE.txt"), "w") as f:
        f.write("Please copy your AMS-InvoSync.exe file to this folder before distributing the package.\n")
        f.write("Then delete this file.")
    print("Added placeholder for .exe file")
    
    # Create the zip file
    zip_filename = "AMS-InvoSync-Package.zip"
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, temp_dir)
                zipf.write(file_path, arcname)
    
    # Clean up the temporary directory
    shutil.rmtree(temp_dir)
    
    print(f"Package created successfully: {zip_filename}")
    print(f"Size: {os.path.getsize(zip_filename) / (1024*1024):.2f} MB")
    print("\nInstructions:")
    print("1. Extract the zip file")
    print("2. Copy your AMS-InvoSync.exe file to the extracted folder")
    print("3. Delete the PLACE_EXE_HERE.txt file")
    print("4. Zip the folder again")
    print("5. Distribute the final zip file to your clients")

if __name__ == "__main__":
    create_simple_package()
