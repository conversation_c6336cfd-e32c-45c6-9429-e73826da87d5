import os
import hashlib
import platform
import uuid
import json
import socket
import re

# License key for the software
VALID_LICENSE_KEY = "AMS-2319-2025"

# Backup directory where license info will be stored
LICENSE_DIR = r"C:\pukcab_XtfoSSMA\xtossmafiles\requried files\pukcab"

# License file name (hidden in the backup directory)
LICENSE_FILE = os.path.join(LICENSE_DIR, ".license_info")

def get_machine_id():
    """
    Get a unique machine ID that won't change even if hardware changes
    This combines multiple system identifiers to create a unique fingerprint
    """
    # Get system information
    system_info = platform.system() + platform.node()

    # Get MAC address (primary network interface)
    try:
        mac = ':'.join(re.findall('..', '%012x' % uuid.getnode()))
        system_info += mac
    except:
        pass

    # Get hostname
    try:
        hostname = socket.gethostname()
        system_info += hostname
    except:
        pass

    # Get disk serial number (Windows only)
    if platform.system() == 'Windows':
        try:
            import wmi
            c = wmi.WMI()
            for disk in c.Win32_LogicalDisk():
                if disk.DeviceID == 'C:':
                    system_info += disk.VolumeSerialNumber
                    break
        except:
            pass

    # Hash the combined information to get a unique machine ID
    return hashlib.sha256(system_info.encode()).hexdigest()

def is_activated():
    """Check if the software is activated on this machine"""
    # Check if license file exists
    if not os.path.exists(LICENSE_FILE):
        return False

    try:
        # Read license data
        with open(LICENSE_FILE, 'r') as f:
            license_data = json.load(f)

        # Check if the machine ID matches
        current_machine_id = get_machine_id()
        if license_data.get('machine_id') != current_machine_id:
            return False

        # Check if the license key is valid
        if license_data.get('license_key') != VALID_LICENSE_KEY:
            return False

        # If we got here, the software is activated
        return True
    except:
        return False

def activate_license(license_key):
    """Activate the software with the given license key"""
    if license_key != VALID_LICENSE_KEY:
        return False, "Invalid license key"

    try:
        # Create the full directory structure if it doesn't exist
        os.makedirs(LICENSE_DIR, exist_ok=True)

        # Also create the backup directory
        backup_dir = r"C:\pukcab_XtfoSSMA"
        os.makedirs(backup_dir, exist_ok=True)

        # Create the full path for Excel backups
        excel_backup_dir = LICENSE_DIR
        os.makedirs(excel_backup_dir, exist_ok=True)

        # Save license information
        license_data = {
            'license_key': license_key,
            'machine_id': get_machine_id(),
            'activation_date': str(os.path.getmtime(LICENSE_DIR) if os.path.exists(LICENSE_DIR) else 0)
        }

        # Save the license file
        with open(LICENSE_FILE, 'w') as f:
            json.dump(license_data, f)

        return True, "Software activated successfully"
    except Exception as e:
        return False, f"Error activating software: {str(e)}"
