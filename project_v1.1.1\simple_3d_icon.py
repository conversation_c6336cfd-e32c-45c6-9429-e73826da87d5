from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 200, 200
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors for modern look
dark_blue = (20, 40, 80)
medium_blue = (30, 60, 120)
light_blue = (40, 80, 160)
white = (255, 255, 255)
light_gray = (220, 220, 220)
dark_gray = (80, 80, 80)

# Create a simple 3D effect by drawing offset rectangles
# Shadow/background layer
draw.rectangle([(55, 55), (width-35, height-35)], fill=dark_gray)

# Main document
draw.rectangle([(40, 40), (width-50, height-50)], fill=medium_blue)

# 3D edge effect
draw.polygon([(width-50, 40), (width-35, 55), (width-35, height-35), (width-50, height-50)], 
            fill=light_blue)
draw.polygon([(40, height-50), (55, height-35), (width-35, height-35), (width-50, height-50)], 
            fill=light_blue)

# Header
draw.rectangle([(40, 40), (width-50, 70)], fill=dark_blue)

# Document lines
for i in range(3):
    y = 90 + (i * 25)
    draw.line([(50, y), (width-60, y)], fill=white, width=2)

# Table
table_y = 170
draw.rectangle([(50, table_y), (width-60, table_y+20)], fill=dark_blue)

# Save the image
image.save("icon.png")

# Save as ICO (32x32 only for simplicity)
icon_image = image.resize((32, 32))
icon_image.save("icon.ico")

print("Simple 3D invoice icon created successfully!")
