import os
import zipfile
import shutil

def create_exe_package():
    """Create a zip package with the executable and required files for AMS-InvoSync"""
    print("Creating AMS-InvoSync EXE installation package...")
    
    # Files to include in the package
    files_to_include = [
        "AMS-InvoSync.exe",  # Your compiled executable
        "icon.ico",
        "icon.png",
        "README_FULL.txt",
        "setup_exe.bat"
    ]
    
    # Create a temporary directory for packaging
    temp_dir = "AMS-InvoSync-EXE-Package"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Copy files to the temporary directory
    for file in files_to_include:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(temp_dir, file))
            print(f"Added {file} to package")
        else:
            print(f"Warning: {file} not found, skipping")
    
    # Rename README_FULL.txt to README.txt
    if os.path.exists(os.path.join(temp_dir, "README_FULL.txt")):
        os.rename(
            os.path.join(temp_dir, "README_FULL.txt"),
            os.path.join(temp_dir, "README.txt")
        )
        print("Renamed README_FULL.txt to README.txt")
    
    # Rename setup_exe.bat to setup.bat
    if os.path.exists(os.path.join(temp_dir, "setup_exe.bat")):
        os.rename(
            os.path.join(temp_dir, "setup_exe.bat"),
            os.path.join(temp_dir, "setup.bat")
        )
        print("Renamed setup_exe.bat to setup.bat")
    
    # Create the zip file
    zip_filename = "AMS-InvoSync-EXE-Setup.zip"
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, temp_dir)
                zipf.write(file_path, arcname)
    
    # Clean up the temporary directory
    shutil.rmtree(temp_dir)
    
    print(f"EXE Package created successfully: {zip_filename}")
    print(f"Size: {os.path.getsize(zip_filename) / (1024*1024):.2f} MB")

if __name__ == "__main__":
    create_exe_package()
