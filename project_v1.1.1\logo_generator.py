import tkinter as tk
from PIL import Image, ImageDraw, ImageFont, ImageTk
import os

def create_logo():
    # Create a new image with a white background
    width, height = 200, 200
    image = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(image)
    
    # Draw a rounded rectangle for the background
    draw.rounded_rectangle([(10, 10), (width-10, height-10)], fill=(41, 128, 185, 230), radius=20)
    
    # Draw a smaller rounded rectangle for the inner part
    draw.rounded_rectangle([(30, 30), (width-30, height-30)], fill=(52, 152, 219, 230), radius=15)
    
    # Try to load a font, fall back to default if not available
    try:
        font_large = ImageFont.truetype("arial.ttf", 40)
        font_small = ImageFont.truetype("arial.ttf", 14)
    except IOError:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Draw the text
    draw.text((width//2, 80), "AMS", fill=(255, 255, 255), font=font_large, anchor="mm")
    draw.text((width//2, 120), "InvoSync", fill=(255, 255, 255), font=font_large, anchor="mm")
    draw.text((width//2, 160), "by AMSSoftX", fill=(255, 255, 255), font=font_small, anchor="mm")
    
    # Save the image
    image.save("icon.png")
    
    # Convert to ICO format for Windows
    icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
    icon_images = []
    
    for size in icon_sizes:
        icon_image = image.resize(size, Image.LANCZOS)
        icon_images.append(icon_image)
    
    # Save as ICO
    icon_images[0].save("icon.ico", format="ICO", sizes=[(image.width, image.height) for image in icon_images])
    
    print("Logo created successfully!")
    print("Files generated: icon.png and icon.ico")
    
    return "icon.ico"

if __name__ == "__main__":
    create_logo()
