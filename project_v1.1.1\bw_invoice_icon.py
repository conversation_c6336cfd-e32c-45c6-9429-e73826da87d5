import tkinter as tk
from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 200, 200
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors - only black and white
black = (0, 0, 0)
white = (255, 255, 255)
light_gray = (230, 230, 230)
medium_gray = (180, 180, 180)
dark_gray = (100, 100, 100)

# Draw a document shape (invoice)
# Main document background
draw.rectangle([(20, 20), (width-20, height-20)], fill=white, outline=black, width=3)

# Header bar
draw.rectangle([(20, 20), (width-20, 50)], fill=black)

# Draw line items (table)
y_pos = 70
# Table header
draw.rectangle([(30, y_pos), (width-30, y_pos+20)], fill=dark_gray)

# Table rows
for i in range(4):
    y_row = y_pos + 25 + (i * 20)
    # Alternating row colors
    if i % 2 == 0:
        draw.rectangle([(30, y_row), (width-30, y_row+20)], fill=white, outline=medium_gray)
    else:
        draw.rectangle([(30, y_row), (width-30, y_row+20)], fill=light_gray, outline=medium_gray)

# Draw total section
y_total = y_pos + 110
draw.line([(width//2, y_total), (width-30, y_total)], fill=black, width=2)
draw.rectangle([(width-80, y_total+10), (width-30, y_total+30)], fill=black)

# Save the image
image.save("invoice_icon.png")

# Create smaller versions for the icon
icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
icon_images = []

for size in icon_sizes:
    resized_img = image.resize(size, Image.LANCZOS)
    icon_images.append(resized_img)

# Save as ICO
icon_images[0].save("icon.ico", format="ICO", sizes=[(img.width, img.height) for img in icon_images])

print("Black and white invoice icon created successfully!")
print("Files generated: invoice_icon.png and icon.ico")
