from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 200, 200
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors - only black and white
black = (0, 0, 0)
white = (255, 255, 255)
light_gray = (230, 230, 230)
medium_gray = (180, 180, 180)

# Draw a document shape (invoice)
# Main document outline
draw.rectangle([(30, 30), (width-30, height-30)], fill=white, outline=black, width=2)

# Header
draw.rectangle([(30, 30), (width-30, 60)], fill=black)

# Draw lines for text content
for i in range(3):
    y = 80 + (i * 20)
    draw.line([(50, y), (width-50, y)], fill=medium_gray, width=1)

# Draw table
table_top = 150
# Table header
draw.rectangle([(40, table_top), (width-40, table_top+20)], fill=black)

# Table rows
for i in range(2):
    y = table_top + 25 + (i * 20)
    draw.rectangle([(40, y), (width-40, y+15)], fill=light_gray if i % 2 == 0 else white, outline=medium_gray)

# Save the image
image.save("icon.png")

# Convert to ICO
try:
    # Resize for icon
    icon_image = image.resize((32, 32))
    icon_image.save("icon.ico")
    print("Icon created: icon.ico")
except Exception as e:
    print(f"Could not create icon: {e}")

print("Black and white invoice icon created successfully!")
