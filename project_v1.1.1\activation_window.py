import tkinter as tk
from tkinter import messagebox, ttk
import os
import sys
from license_utils import activate_license, is_activated, VALID_LICENSE_KEY

class ActivationWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("AMS-InvoSync Activation")
        self.root.geometry("500x300")
        self.root.resizable(False, False)

        # Center the window
        self.center_window()

        # Set window icon if available
        try:
            if os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
                print("Applied icon to activation window")
        except Exception as e:
            print(f"Could not set icon: {e}")

        # Create main frame
        self.main_frame = tk.Frame(self.root, padx=20, pady=20)
        self.main_frame.pack(fill="both", expand=True)

        # Add title
        title_label = tk.Label(
            self.main_frame,
            text="AMS-InvoSync Activation",
            font=("Segoe UI", 16, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Add welcome message
        welcome_label = tk.Label(
            self.main_frame,
            text="Welcome to AMS-InvoSync! Please enter your license key to activate the software.",
            font=("Segoe UI", 10),
            wraplength=450,
            justify="center"
        )
        welcome_label.pack(pady=(0, 20))

        # License key entry
        license_frame = tk.Frame(self.main_frame)
        license_frame.pack(fill="x", pady=(0, 20))

        license_label = tk.Label(
            license_frame,
            text="License Key:",
            font=("Segoe UI", 10, "bold")
        )
        license_label.pack(side="left", padx=(0, 10))

        self.license_entry = tk.Entry(
            license_frame,
            font=("Segoe UI", 10),
            width=30
        )
        self.license_entry.pack(side="left")

        # Activate button
        self.activate_button = tk.Button(
            self.main_frame,
            text="Activate",
            font=("Segoe UI", 10, "bold"),
            bg="#4CAF50",
            fg="white",
            padx=20,
            pady=5,
            command=self.activate
        )
        self.activate_button.pack(pady=(0, 20))

        # Footer
        footer_label = tk.Label(
            self.main_frame,
            text="Developed by AMSSoftX | https://amssoftx.com",
            font=("Segoe UI", 8),
            fg="gray"
        )
        footer_label.pack(side="bottom")

        # Set focus to license entry
        self.license_entry.focus_set()

        # Bind Enter key to activate button
        self.root.bind("<Return>", lambda event: self.activate())

    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def activate(self):
        """Activate the software with the entered license key"""
        license_key = self.license_entry.get().strip()

        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return

        # Show loading indicator
        self.activate_button.config(text="Activating...", state="disabled")
        self.root.update_idletasks()

        # Activate the license
        success, message = activate_license(license_key)

        if success:
            messagebox.showinfo("Success", message)
            self.root.destroy()  # Close the activation window
        else:
            messagebox.showerror("Error", message)
            self.activate_button.config(text="Activate", state="normal")
            self.license_entry.focus_set()

def show_activation_window():
    """Show the activation window and return True if activated, False otherwise"""
    # Check if already activated
    if is_activated():
        return True

    # Create and show activation window
    activation_root = tk.Tk()
    activation_window = ActivationWindow(activation_root)
    activation_root.mainloop()

    # Check if activation was successful
    return is_activated()

if __name__ == "__main__":
    # For testing
    if show_activation_window():
        print("Software activated successfully")
    else:
        print("Software not activated")
        sys.exit(0)
