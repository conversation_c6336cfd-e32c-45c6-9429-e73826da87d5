from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os

# Create a new image with a transparent background
width, height = 512, 512  # Larger size for better detail
image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
draw = ImageDraw.Draw(image)

# Define colors for modern look
dark_blue = (20, 40, 80)
medium_blue = (30, 60, 120)
light_blue = (40, 80, 160)
white = (255, 255, 255)
light_gray = (220, 220, 220)
shadow_color = (10, 10, 10, 100)  # Semi-transparent black for shadows

# Create base document with 3D effect (multiple layers with offset)
# Shadow layer (blurred black rectangle offset slightly)
shadow = Image.new('RGBA', (width, height), (0, 0, 0, 0))
shadow_draw = ImageDraw.Draw(shadow)
shadow_draw.rectangle([(60, 60), (width-40, height-40)], fill=shadow_color)
shadow = shadow.filter(ImageFilter.GaussianBlur(15))  # Blur the shadow
image.paste(shadow, (0, 0), shadow)

# Base document - dark blue
draw.rectangle([(50, 50), (width-50, height-50)], fill=dark_blue, outline=None)

# 3D effect for document (side edge)
draw.polygon([(width-50, 50), (width-30, 70), (width-30, height-30), (width-50, height-50)], 
            fill=medium_blue)
draw.polygon([(50, height-50), (70, height-30), (width-30, height-30), (width-50, height-50)], 
            fill=medium_blue)

# Main document face - slightly lighter blue
draw.rectangle([(50, 50), (width-50, height-50)], fill=medium_blue, outline=None)

# Header with gradient effect
for i in range(50):
    # Create a gradient from dark to medium blue
    color = (
        int(dark_blue[0] + (medium_blue[0] - dark_blue[0]) * (i / 50)),
        int(dark_blue[1] + (medium_blue[1] - dark_blue[1]) * (i / 50)),
        int(dark_blue[2] + (medium_blue[2] - dark_blue[2]) * (i / 50))
    )
    draw.line([(50, 50 + i), (width-50, 50 + i)], fill=color)

# Draw modern invoice elements
# Header bar with 3D effect
draw.rectangle([(70, 70), (width-70, 120)], fill=light_blue, outline=None)
# Highlight on top edge for 3D effect
draw.line([(70, 70), (width-70, 70)], fill=white, width=2)

# Document lines with 3D effect
for i in range(3):
    y = 150 + (i * 60)
    # Shadow line
    draw.line([(80, y+3), (width-80, y+3)], fill=dark_blue, width=4)
    # Main line
    draw.line([(80, y), (width-80, y)], fill=light_gray, width=4)

# Table with 3D effect
table_y = 330
# Table header with 3D effect
draw.rectangle([(80, table_y), (width-80, table_y+40)], fill=light_blue, outline=None)
# Highlight on top edge for 3D effect
draw.line([(80, table_y), (width-80, table_y)], fill=white, width=2)
# Shadow on bottom edge for 3D effect
draw.line([(80, table_y+40), (width-80, table_y+40)], fill=dark_blue, width=2)

# Table rows with 3D effect
for i in range(2):
    y = table_y + 50 + (i * 40)
    # Row background
    draw.rectangle([(80, y), (width-80, y+35)], fill=light_gray if i % 2 == 0 else white)
    # Highlight on top edge for 3D effect
    draw.line([(80, y), (width-80, y)], fill=white, width=1)
    # Shadow on bottom edge for 3D effect
    draw.line([(80, y+35), (width-80, y+35)], fill=medium_blue, width=1)

# Save the image
image.save("modern_icon.png")

# Create smaller versions for the icon
icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
icon_images = []

for size in icon_sizes:
    resized_img = image.resize(size, Image.LANCZOS)
    icon_images.append(resized_img)

# Save as ICO
try:
    # Try to save with multiple sizes
    icon_images[0].save("icon.ico", format="ICO", sizes=[(img.width, img.height) for img in icon_images])
except:
    # Fallback to single size if multiple sizes fail
    icon_images[1].save("icon.ico")  # Use 32x32 as fallback

print("Modern 3D invoice icon created successfully!")
