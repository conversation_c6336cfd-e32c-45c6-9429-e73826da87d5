import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import os

# Create a new image with a transparent background
width, height = 256, 256
image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
draw = ImageDraw.Draw(image)

# Define colors
primary_color = (41, 128, 185)  # Blue
secondary_color = (52, 152, 219)  # Lighter blue
accent_color = (231, 76, 60)    # Red
white = (255, 255, 255)
dark_gray = (52, 73, 94)

# Draw a document shape (invoice)
# Main document
draw.rounded_rectangle([(40, 30), (width-40, height-30)], fill=white, radius=15, outline=primary_color, width=3)

# Header bar
draw.rounded_rectangle([(40, 30), (width-40, 80)], fill=primary_color, radius=15)

# Draw invoice title
try:
    # Try to use a nice font
    font_title = ImageFont.truetype("arial.ttf", 24)
    font_text = ImageFont.truetype("arial.ttf", 16)
    font_number = ImageFont.truetype("arial.ttf", 36)
except:
    # Fall back to default font
    font_title = ImageFont.load_default()
    font_text = ImageFont.load_default()
    font_number = ImageFont.load_default()

# Draw "INVOICE" text in header
draw.text((width//2, 55), "INVOICE", fill=white, font=font_title, anchor="mm")

# Draw invoice details
y_pos = 100
draw.text((60, y_pos), "Invoice #:", fill=dark_gray, font=font_text)
y_pos += 30
draw.text((60, y_pos), "Date:", fill=dark_gray, font=font_text)
y_pos += 30
draw.text((60, y_pos), "Due Date:", fill=dark_gray, font=font_text)

# Draw a big invoice number
draw.text((width-80, 115), "2023", fill=accent_color, font=font_number)

# Draw line items (table)
y_pos = 180
# Table header
draw.rectangle([(50, y_pos), (width-50, y_pos+30)], fill=secondary_color)
draw.text((70, y_pos+15), "Item", fill=white, font=font_text, anchor="lm")
draw.text((width-70, y_pos+15), "Amount", fill=white, font=font_text, anchor="rm")

# Table rows
for i in range(3):
    y_row = y_pos + 30 + (i * 25)
    # Alternating row colors
    if i % 2 == 0:
        draw.rectangle([(50, y_row), (width-50, y_row+25)], fill=(240, 240, 240))
    
    # Draw line
    draw.line([(50, y_row+25), (width-50, y_row+25)], fill=(200, 200, 200), width=1)

# Draw total section
y_total = y_pos + 115
draw.line([(width//2, y_total), (width-50, y_total)], fill=dark_gray, width=2)
draw.text((width-120, y_total+20), "TOTAL:", fill=dark_gray, font=font_text, anchor="rm")
draw.text((width-70, y_total+20), "₹10,000", fill=accent_color, font=font_text, anchor="rm")

# Save the image
image.save("invoice_icon.png")

# Create smaller versions for the icon
icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
icon_images = []

for size in icon_sizes:
    resized_img = image.resize(size, Image.LANCZOS)
    icon_images.append(resized_img)

# Save as ICO
icon_images[0].save("icon.ico", format="ICO", sizes=[(img.width, img.height) for img in icon_images])

print("Invoice icon created successfully!")
print("Files generated: invoice_icon.png and icon.ico")
