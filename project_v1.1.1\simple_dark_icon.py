from PIL import Image, ImageDraw

# Create a new image with a dark background
width, height = 200, 200
image = Image.new('RGB', (width, height), (30, 30, 30))  # Very dark gray background
draw = ImageDraw.Draw(image)

# Define colors
white = (255, 255, 255)
dark_gray = (50, 50, 50)
light_gray = (180, 180, 180)

# Draw a document shape (invoice) with white outline on dark background
# Main document outline
draw.rectangle([(40, 40), (width-40, height-40)], fill=dark_gray, outline=white, width=3)

# Header
draw.rectangle([(40, 40), (width-40, 70)], fill=(20, 20, 20), outline=white, width=1)

# Draw lines for text content - white lines on dark background
for i in range(3):
    y = 90 + (i * 25)
    draw.line([(50, y), (width-50, y)], fill=light_gray, width=2)

# Draw a simple table
table_y = 170
# Table header
draw.rectangle([(50, table_y), (width-50, table_y+20)], fill=(20, 20, 20), outline=white, width=1)

# Save the image
image.save("icon.png")

# Save as ICO (32x32 only for simplicity)
icon_image = image.resize((32, 32))
icon_image.save("icon.ico")

print("Dark invoice icon created successfully!")
