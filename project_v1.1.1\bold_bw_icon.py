from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 256, 256  # Larger size for better clarity
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors - only black and white with high contrast
black = (0, 0, 0)
white = (255, 255, 255)
light_gray = (220, 220, 220)

# Draw a document shape (invoice) with thicker borders
# Main document outline
draw.rectangle([(30, 30), (width-30, height-30)], fill=white, outline=black, width=5)

# Header
draw.rectangle([(30, 30), (width-30, 70)], fill=black)

# Draw "INVOICE" text in header (using shapes since we can't rely on fonts)
# I
draw.rectangle([(width//2 - 50, 40), (width//2 - 40, 60)], fill=white)
# N
draw.rectangle([(width//2 - 30, 40), (width//2 - 20, 60)], fill=white)
draw.rectangle([(width//2 - 30, 40), (width//2 - 10, 60)], fill=white, outline=white, width=1)
draw.rectangle([(width//2 - 10, 40), (width//2, 60)], fill=white)
# V
draw.polygon([(width//2 + 10, 40), (width//2 + 20, 60), (width//2 + 30, 40)], fill=white)

# Draw bold lines for text content
for i in range(3):
    y = 90 + (i * 30)
    draw.line([(50, y), (width-50, y)], fill=black, width=3)

# Draw table with bold lines
table_top = 180
# Table header
draw.rectangle([(40, table_top), (width-40, table_top+30)], fill=black, outline=black, width=3)

# Table rows with alternating colors
for i in range(2):
    y = table_top + 35 + (i * 30)
    draw.rectangle([(40, y), (width-40, y+25)], 
                  fill=light_gray if i % 2 == 0 else white, 
                  outline=black, width=2)

# Draw total section with bold line
y_total = table_top + 100
draw.line([(width//2, y_total), (width-40, y_total)], fill=black, width=4)
draw.rectangle([(width-100, y_total+10), (width-40, y_total+40)], fill=black, outline=black, width=3)

# Save the image
image.save("icon.png")

# Convert to ICO with multiple sizes
sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)]
icon_images = []

for size in sizes:
    resized = image.resize(size, Image.LANCZOS)
    icon_images.append(resized)

# Save as ICO with multiple sizes
image.save("icon.ico", format="ICO", sizes=sizes)
print("Bold black and white invoice icon created successfully!")
