from PIL import Image, ImageDraw
import os

# Create a new image with a white background
width, height = 256, 256
image = Image.new('RGB', (width, height), (255, 255, 255))
draw = ImageDraw.Draw(image)

# Define colors
black = (0, 0, 0)
white = (255, 255, 255)

# Draw a very simple invoice icon with thick lines
# Main document outline
draw.rectangle([(40, 40), (width-40, height-40)], fill=white, outline=black, width=8)

# Header
draw.rectangle([(40, 40), (width-40, 80)], fill=black)

# Draw lines for text content - very thick and clear
for i in range(3):
    y = 100 + (i * 40)
    draw.line([(60, y), (width-60, y)], fill=black, width=6)

# Draw a simple table
table_y = 220
# Table header
draw.rectangle([(60, table_y), (width-60, table_y+30)], fill=black)

# Save the image
image.save("icon.png")

# Save as ICO
image.save("icon.ico", sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128)])
print("Very simple but clear icon created successfully!")
