import os
import subprocess
import sys

def check_pyinstaller():
    """Check if PyInstaller is installed, if not install it."""
    try:
        import PyInstaller
        print("PyInstaller is already installed.")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller installed successfully.")

def create_exe():
    """Create executable from AMS-InvoSync.py"""
    print("Creating executable file for AMS-InvoSync...")
    
    # Command to create a single executable file with icon
    cmd = [
        "pyinstaller",
        "--onefile",  # Create a single executable file
        "--windowed",  # Do not show console window when running the app
        "--icon=icon.ico",  # Use the icon file
        "--name=AMS-InvoSync",  # Name of the executable
        "--add-data=icon.ico;.",  # Include icon file
        "--add-data=icon.png;.",  # Include icon image
        "AMS-InvoSync.py"  # Main script file
    ]
    
    # Run PyInstaller
    subprocess.check_call(cmd)
    
    print("Executable created successfully!")
    print("You can find it in the 'dist' folder.")

if __name__ == "__main__":
    # Check and install PyInstaller if needed
    check_pyinstaller()
    
    # Create the executable
    create_exe()
